use crate::{
    models::{UserState, PaymentStatus, UserEnvironmentState},
    services::{ServiceResult, ServiceError, state_management::StateManagementService},
    database::Database,
    config::Config,
};
use chrono::{DateTime, Utc, Duration};
use std::collections::HashMap;
use tracing::{info, warn, error, instrument};
use mongodb::bson;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentEvent {
    pub user_id: String,
    pub event_type: PaymentEventType,
    pub amount: f64,
    pub currency: String,
    pub timestamp: DateTime<Utc>,
    pub payment_method: String,
    pub transaction_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PaymentEventType {
    PaymentReceived,
    PaymentFailed,
    PaymentOverdue,
    SubscriptionCancelled,
    SubscriptionReactivated,
    RefundProcessed,
    ChargebackReceived,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PaymentAccessPolicy {
    pub grace_period_hours: u32,
    pub suspension_after_days: u32,
    pub data_retention_days: u32,
    pub auto_reactivation: bool,
    pub trial_period_days: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPaymentProfile {
    pub user_id: String,
    pub payment_status: PaymentStatus,
    pub current_plan: String,
    pub last_payment_date: Option<DateTime<Utc>>,
    pub next_billing_date: Option<DateTime<Utc>>,
    pub overdue_amount: f64,
    pub grace_period_expires: Option<DateTime<Utc>>,
    pub suspension_date: Option<DateTime<Utc>>,
    pub trial_expires: Option<DateTime<Utc>>,
    pub payment_history: Vec<PaymentEvent>,
}

pub struct PaymentAccessControlService {
    database: Database,
    config: Config,
    state_service: StateManagementService,
    policy: PaymentAccessPolicy,
}

impl PaymentAccessControlService {
    pub fn new(database: &Database, config: &Config) -> Self {
        let policy = PaymentAccessPolicy {
            grace_period_hours: 72,    // 3 days grace period
            suspension_after_days: 7,   // Suspend after 7 days overdue
            data_retention_days: 30,    // Keep data for 30 days after suspension
            auto_reactivation: true,    // Automatically reactivate on payment
            trial_period_days: 14,      // 14-day trial period
        };

        let state_service = StateManagementService::new(database, config);

        Self {
            database: database.clone(),
            config: config.clone(),
            state_service,
            policy,
        }
    }

    /// Process payment event and update user access
    #[instrument(skip(self))]
    pub async fn process_payment_event(&self, event: PaymentEvent) -> ServiceResult<()> {
        info!("Processing payment event for user {}: {:?}", event.user_id, event.event_type);

        let mut user_profile = self.get_user_payment_profile(&event.user_id).await?;
        user_profile.payment_history.push(event.clone());

        match event.event_type {
            PaymentEventType::PaymentReceived => {
                self.handle_payment_received(&mut user_profile, &event).await?;
            },
            PaymentEventType::PaymentFailed => {
                self.handle_payment_failed(&mut user_profile, &event).await?;
            },
            PaymentEventType::PaymentOverdue => {
                self.handle_payment_overdue(&mut user_profile, &event).await?;
            },
            PaymentEventType::SubscriptionCancelled => {
                self.handle_subscription_cancelled(&mut user_profile, &event).await?;
            },
            PaymentEventType::SubscriptionReactivated => {
                self.handle_subscription_reactivated(&mut user_profile, &event).await?;
            },
            PaymentEventType::RefundProcessed => {
                self.handle_refund_processed(&mut user_profile, &event).await?;
            },
            PaymentEventType::ChargebackReceived => {
                self.handle_chargeback_received(&mut user_profile, &event).await?;
            },
        }

        // Update user profile in database
        self.update_user_payment_profile(&user_profile).await?;

        info!("Payment event processed successfully for user {}", event.user_id);
        Ok(())
    }

    /// Handle successful payment
    async fn handle_payment_received(&self, profile: &mut UserPaymentProfile, event: &PaymentEvent) -> ServiceResult<()> {
        profile.payment_status = PaymentStatus::Active;
        profile.last_payment_date = Some(event.timestamp);
        profile.overdue_amount = 0.0;
        profile.grace_period_expires = None;
        profile.suspension_date = None;

        // Calculate next billing date (monthly billing)
        profile.next_billing_date = Some(event.timestamp + Duration::days(30));

        // Reactivate user if they were suspended or cold
        let current_state = self.get_user_current_state(&profile.user_id).await?;
        if current_state == UserState::Cold {
            info!("Reactivating user {} after payment", profile.user_id);
            
            self.state_service.execute_state_transition(
                &profile.user_id,
                UserState::Hot,
                crate::services::state_management::TransitionTrigger::AutomaticPayment,
                Some("Payment received - reactivating account".to_string()),
            ).await?;

            // Send reactivation notification
            self.send_reactivation_notification(&profile.user_id).await?;
        }

        Ok(())
    }

    /// Handle failed payment
    async fn handle_payment_failed(&self, profile: &mut UserPaymentProfile, event: &PaymentEvent) -> ServiceResult<()> {
        warn!("Payment failed for user {}", profile.user_id);

        // Start grace period if not already started
        if profile.grace_period_expires.is_none() {
            profile.grace_period_expires = Some(event.timestamp + Duration::hours(self.policy.grace_period_hours as i64));
            
            // Send payment failure notification
            self.send_payment_failure_notification(&profile.user_id, profile.grace_period_expires.unwrap()).await?;
        }

        Ok(())
    }

    /// Handle overdue payment
    async fn handle_payment_overdue(&self, profile: &mut UserPaymentProfile, event: &PaymentEvent) -> ServiceResult<()> {
        profile.payment_status = PaymentStatus::Overdue;
        profile.overdue_amount = event.amount;

        // Check if grace period has expired
        if let Some(grace_expires) = profile.grace_period_expires {
            if Utc::now() > grace_expires {
                // Move to suspension
                profile.payment_status = PaymentStatus::Suspended;
                profile.suspension_date = Some(Utc::now());

                // Transition user to cold state
                self.state_service.execute_state_transition(
                    &profile.user_id,
                    UserState::Cold,
                    crate::services::state_management::TransitionTrigger::AutomaticPayment,
                    Some("Payment overdue - account suspended".to_string()),
                ).await?;

                // Send suspension notification
                self.send_suspension_notification(&profile.user_id).await?;
            }
        }

        Ok(())
    }

    /// Handle subscription cancellation
    async fn handle_subscription_cancelled(&self, profile: &mut UserPaymentProfile, _event: &PaymentEvent) -> ServiceResult<()> {
        info!("Subscription cancelled for user {}", profile.user_id);

        // Allow user to continue until next billing date
        if let Some(next_billing) = profile.next_billing_date {
            if Utc::now() < next_billing {
                // User can continue until billing period ends
                profile.payment_status = PaymentStatus::Active;
            } else {
                // Billing period ended, suspend immediately
                profile.payment_status = PaymentStatus::Suspended;
                profile.suspension_date = Some(Utc::now());

                self.state_service.execute_state_transition(
                    &profile.user_id,
                    UserState::Cold,
                    crate::services::state_management::TransitionTrigger::AutomaticPayment,
                    Some("Subscription cancelled".to_string()),
                ).await?;
            }
        }

        Ok(())
    }

    /// Handle subscription reactivation
    async fn handle_subscription_reactivated(&self, profile: &mut UserPaymentProfile, event: &PaymentEvent) -> ServiceResult<()> {
        profile.payment_status = PaymentStatus::Active;
        profile.suspension_date = None;
        profile.grace_period_expires = None;
        profile.next_billing_date = Some(event.timestamp + Duration::days(30));

        // Reactivate user
        self.state_service.execute_state_transition(
            &profile.user_id,
            UserState::Hot,
            crate::services::state_management::TransitionTrigger::AutomaticPayment,
            Some("Subscription reactivated".to_string()),
        ).await?;

        Ok(())
    }

    /// Handle refund processing
    async fn handle_refund_processed(&self, profile: &mut UserPaymentProfile, event: &PaymentEvent) -> ServiceResult<()> {
        warn!("Refund processed for user {}: ${}", profile.user_id, event.amount);

        // Depending on refund policy, may need to suspend user
        // For now, just log the event
        info!("Refund of ${} processed for user {}", event.amount, profile.user_id);

        Ok(())
    }

    /// Handle chargeback
    async fn handle_chargeback_received(&self, profile: &mut UserPaymentProfile, event: &PaymentEvent) -> ServiceResult<()> {
        error!("Chargeback received for user {}: ${}", profile.user_id, event.amount);

        // Immediately suspend user on chargeback
        profile.payment_status = PaymentStatus::Suspended;
        profile.suspension_date = Some(Utc::now());

        self.state_service.execute_state_transition(
            &profile.user_id,
            UserState::Cold,
            crate::services::state_management::TransitionTrigger::AutomaticPayment,
            Some("Chargeback received - account suspended".to_string()),
        ).await?;

        // Send chargeback notification to admin
        self.send_chargeback_notification(&profile.user_id, event.amount).await?;

        Ok(())
    }

    /// Run periodic payment status checks
    #[instrument(skip(self))]
    pub async fn run_payment_status_check(&self) -> ServiceResult<PaymentCheckResults> {
        info!("Running periodic payment status check");

        let mut results = PaymentCheckResults {
            users_checked: 0,
            suspensions: 0,
            reactivations: 0,
            grace_period_started: 0,
            data_cleanups: 0,
        };

        let all_users = self.get_all_user_payment_profiles().await?;

        for mut profile in all_users {
            results.users_checked += 1;

            // Check for expired grace periods
            if let Some(grace_expires) = profile.grace_period_expires {
                if Utc::now() > grace_expires && profile.payment_status != PaymentStatus::Suspended {
                    profile.payment_status = PaymentStatus::Suspended;
                    profile.suspension_date = Some(Utc::now());

                    self.state_service.execute_state_transition(
                        &profile.user_id,
                        UserState::Cold,
                        crate::services::state_management::TransitionTrigger::AutomaticPayment,
                        Some("Grace period expired".to_string()),
                    ).await?;

                    results.suspensions += 1;
                    self.update_user_payment_profile(&profile).await?;
                }
            }

            // Check for expired trials
            if let Some(trial_expires) = profile.trial_expires {
                if Utc::now() > trial_expires && profile.payment_status == PaymentStatus::Trial {
                    profile.payment_status = PaymentStatus::Overdue;
                    profile.grace_period_expires = Some(Utc::now() + Duration::hours(self.policy.grace_period_hours as i64));

                    results.grace_period_started += 1;
                    self.update_user_payment_profile(&profile).await?;
                    self.send_trial_expired_notification(&profile.user_id).await?;
                }
            }

            // Check for data cleanup (suspended users past retention period)
            if let Some(suspension_date) = profile.suspension_date {
                let retention_expires = suspension_date + Duration::days(self.policy.data_retention_days as i64);
                if Utc::now() > retention_expires {
                    self.schedule_data_cleanup(&profile.user_id).await?;
                    results.data_cleanups += 1;
                }
            }
        }

        info!("Payment status check completed: {:?}", results);
        Ok(results)
    }

    /// Create trial user
    #[instrument(skip(self))]
    pub async fn create_trial_user(&self, user_id: &str, plan: &str) -> ServiceResult<UserPaymentProfile> {
        let trial_expires = Utc::now() + Duration::days(self.policy.trial_period_days as i64);

        let profile = UserPaymentProfile {
            user_id: user_id.to_string(),
            payment_status: PaymentStatus::Trial,
            current_plan: plan.to_string(),
            last_payment_date: None,
            next_billing_date: Some(trial_expires),
            overdue_amount: 0.0,
            grace_period_expires: None,
            suspension_date: None,
            trial_expires: Some(trial_expires),
            payment_history: vec![],
        };

        self.update_user_payment_profile(&profile).await?;
        self.send_trial_welcome_notification(user_id, trial_expires).await?;

        info!("Created trial user {} with {} day trial", user_id, self.policy.trial_period_days);
        Ok(profile)
    }

    /// Check if user has access based on payment status
    pub async fn check_user_access(&self, user_id: &str) -> ServiceResult<bool> {
        let profile = self.get_user_payment_profile(user_id).await?;
        
        match profile.payment_status {
            PaymentStatus::Active | PaymentStatus::Trial => Ok(true),
            PaymentStatus::Overdue => {
                // Check if still in grace period
                if let Some(grace_expires) = profile.grace_period_expires {
                    Ok(Utc::now() <= grace_expires)
                } else {
                    Ok(false)
                }
            },
            PaymentStatus::Suspended => Ok(false),
        }
    }

    // Notification methods
    async fn send_payment_failure_notification(&self, user_id: &str, grace_expires: DateTime<Utc>) -> ServiceResult<()> {
        info!("Sending payment failure notification to user {}, grace period expires: {}", user_id, grace_expires);
        // TODO: Implement actual notification sending
        Ok(())
    }

    async fn send_suspension_notification(&self, user_id: &str) -> ServiceResult<()> {
        info!("Sending suspension notification to user {}", user_id);
        // TODO: Implement actual notification sending
        Ok(())
    }

    async fn send_reactivation_notification(&self, user_id: &str) -> ServiceResult<()> {
        info!("Sending reactivation notification to user {}", user_id);
        // TODO: Implement actual notification sending
        Ok(())
    }

    async fn send_chargeback_notification(&self, user_id: &str, amount: f64) -> ServiceResult<()> {
        error!("Sending chargeback notification for user {}, amount: ${}", user_id, amount);
        // TODO: Implement admin notification
        Ok(())
    }

    async fn send_trial_expired_notification(&self, user_id: &str) -> ServiceResult<()> {
        info!("Sending trial expired notification to user {}", user_id);
        // TODO: Implement actual notification sending
        Ok(())
    }

    async fn send_trial_welcome_notification(&self, user_id: &str, trial_expires: DateTime<Utc>) -> ServiceResult<()> {
        info!("Sending trial welcome notification to user {}, expires: {}", user_id, trial_expires);
        // TODO: Implement actual notification sending
        Ok(())
    }

    async fn schedule_data_cleanup(&self, user_id: &str) -> ServiceResult<()> {
        warn!("Scheduling data cleanup for user {}", user_id);
        // TODO: Implement data cleanup scheduling
        Ok(())
    }

    // Database operations (to be implemented)
    async fn get_user_payment_profile(&self, user_id: &str) -> ServiceResult<UserPaymentProfile> {
        // TODO: Implement database query
        Ok(UserPaymentProfile {
            user_id: user_id.to_string(),
            payment_status: PaymentStatus::Active,
            current_plan: "nano".to_string(),
            last_payment_date: Some(Utc::now() - Duration::days(15)),
            next_billing_date: Some(Utc::now() + Duration::days(15)),
            overdue_amount: 0.0,
            grace_period_expires: None,
            suspension_date: None,
            trial_expires: None,
            payment_history: vec![],
        })
    }

    async fn get_all_user_payment_profiles(&self) -> ServiceResult<Vec<UserPaymentProfile>> {
        // TODO: Implement database query
        Ok(vec![])
    }

    async fn update_user_payment_profile(&self, profile: &UserPaymentProfile) -> ServiceResult<()> {
        // TODO: Implement database update
        info!("Updated payment profile for user {}", profile.user_id);
        Ok(())
    }

    async fn get_user_current_state(&self, user_id: &str) -> ServiceResult<UserState> {
        // TODO: Implement database query
        Ok(UserState::Hot)
    }

    /// Get count of users with payment issues for orchestration metrics
    pub async fn get_payment_issues_count(&self) -> ServiceResult<u32> {
        let collection = self.database.collection::<UserPaymentProfile>("user_payment_profiles");

        let count = collection
            .count_documents(
                bson::doc! {
                    "payment_status": {
                        "$in": ["overdue", "suspended"]
                    }
                },
                None
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        Ok(count as u32)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentCheckResults {
    pub users_checked: u32,
    pub suspensions: u32,
    pub reactivations: u32,
    pub grace_period_started: u32,
    pub data_cleanups: u32,
}
