use crate::{
    controllers,
    middleware::{
        auth::auth_middleware,
        json_error_handling_middleware,
        request_id_middleware,
        timing_middleware
    },
    models::common::{ApiResponse, ResponseMetadata},
    AppState
};
use axum::{
    middleware,
    routing::{delete, get, patch, post, put},
    Router,
    response::{Json, IntoResponse},
    http::StatusCode,
};
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::trace::TraceLayer;

pub fn create_router(state: Arc<AppState>) -> Router {
    Router::new()
        // Public routes (no authentication required)
        .nest("/api/v1/public", public_routes())

        // User routes (authentication required)
        .nest("/api/v1/users", user_routes().layer(middleware::from_fn_with_state(state.clone(), auth_middleware)))

        // Admin routes (admin authentication required)
        .nest("/api/v1/admin", admin_routes().layer(middleware::from_fn_with_state(state.clone(), auth_middleware)))

        // Legacy health check routes (for backward compatibility)
        .route("/health", get(controllers::health::health_check))
        .route("/api/v1/health", get(controllers::health::health_check))

        // Fallback handler for 404s
        .fallback(handle_404)

        // Add state and middleware layers
        .with_state(state)
        .layer(ServiceBuilder::new()
            .layer(middleware::from_fn(request_id_middleware))
            .layer(middleware::from_fn(timing_middleware))
            .layer(middleware::from_fn(json_error_handling_middleware))
            .layer(TraceLayer::new_for_http())
        )
}

fn public_routes() -> Router<Arc<AppState>> {
    Router::new()
        // Health check
        .route("/health", get(controllers::health::health_check))

        // Authentication routes
        .route("/auth/register", post(controllers::auth::register))
        .route("/auth/login", post(controllers::auth::login))
        .route("/auth/refresh", post(controllers::auth::refresh_token))

        // Webhook routes (public endpoints for external services)
        .route("/webhooks/github", post(controllers::webhooks::handle_github_webhook))
        .route("/webhooks/gitlab", post(controllers::webhooks::handle_gitlab_webhook))
        .route("/webhooks/git/:app_id", post(controllers::webhooks::handle_application_webhook))
        .route("/webhooks/health", get(controllers::webhooks::webhook_health_check))
        .route("/webhooks", post(controllers::webhooks::handle_generic_webhook))

        // Public information endpoints
        .route("/african-regions", get(controllers::regions::list_regions))
        .route("/african-regions/:region_code", get(controllers::regions::get_region))
        .route("/african-regions/:region_code/pricing", get(controllers::regions::get_region_pricing))
        .route("/countries", get(controllers::regions::list_countries))
        .route("/countries/:country_code/regions", get(controllers::regions::get_country_regions))

        // Public hosting information
        .route("/hosting/plans", get(controllers::hosting_plans::list_hosting_plans))
        .route("/hosting/plans/recommend", get(controllers::hosting_plans::get_recommended_plan))
        .route("/hosting/plans/:plan_name", get(controllers::hosting_plans::get_plan_by_name))
        .route("/hosting/pricing/:country_code", get(controllers::intelligent_hosting::get_african_pricing))
        .route("/hosting/health", get(controllers::intelligent_hosting::health_check))

        // SSH Authentication (Public for server-side SSH validation)
        .route("/shared-hosting/ssh-auth/:user_id", post(controllers::shared_hosting::authenticate_ssh_access))
}

fn user_routes() -> Router<Arc<AppState>> {
    Router::new()
        // User Profile Management
        .route("/profile", get(controllers::users::get_profile))
        // .route("/profile", put(controllers::users::update_profile))

        // Platform Services - Applications
        .route("/applications", get(controllers::applications::list_applications))
        .route("/applications", post(controllers::applications::create_application))
        .route("/applications/simple", post(controllers::applications::create_simple_application))
        .route("/applications/:app_id", get(controllers::applications::get_application))
        .route("/applications/:app_id", put(controllers::applications::update_application))
        .route("/applications/:app_id", delete(controllers::applications::delete_application))
        .route("/applications/:app_id/deploy", post(controllers::applications::trigger_deployment))
        .route("/applications/:app_id/deployments", get(controllers::applications::list_deployments))
        .route("/applications/:app_id/deployments/:deployment_id", get(controllers::applications::get_deployment))
        .route("/applications/:app_id/deployments/:deployment_id/rollback", post(controllers::applications::rollback_deployment))

        // Platform Services - Environment & Secrets
        .route("/environment-groups", get(controllers::environment::list_environment_groups))
        .route("/environment-groups", post(controllers::environment::create_environment_group))
        .route("/environment-groups/:group_id", get(controllers::environment::get_environment_group))
        .route("/environment-groups/:group_id/variables", post(controllers::environment::set_environment_variable))
        .route("/environment-groups/:group_id/variables/:key", delete(controllers::environment::delete_environment_variable))
        .route("/environment-groups/:group_id/secrets", post(controllers::environment::set_secret))
        .route("/environment-groups/:group_id/secrets/:key", delete(controllers::environment::delete_secret))
        .route("/environment-groups/:group_id/link", post(controllers::environment::link_application))

        // Platform Services - Logs & Monitoring
        .route("/applications/:app_id/logs", get(controllers::logs::get_application_logs))
        .route("/applications/:app_id/logs/stream", get(controllers::logs::stream_application_logs))
        .route("/applications/:app_id/deployments/:deployment_id/logs", get(controllers::logs::get_deployment_logs))
        .route("/builds/:job_id/logs", get(controllers::logs::get_build_logs))
        .route("/builds/:job_id/logs/stream", get(controllers::logs::stream_build_logs))

        // User Account & Billing
        .route("/account", get(controllers::vultr::get_account))
        .route("/account/usage", get(controllers::vultr::get_account_bandwidth))
        .route("/billing", get(controllers::billing::get_billing_info))
        .route("/billing/invoices", get(controllers::billing::list_invoices))
        .route("/billing/invoices/:id", get(controllers::billing::get_invoice))
        .route("/billing/usage", get(controllers::billing::get_usage))
        .route("/billing/calculate/:plan_name", get(controllers::hosting_plans::calculate_pricing))

        // User Infrastructure Management
        .route("/instances", get(controllers::instances::list_instances))
        .route("/instances/:id", get(controllers::instances::get_instance))
        .route("/instances/:id", delete(controllers::instances::delete_instance))
        .route("/instances/:id/start", post(controllers::instances::start_instance))
        .route("/instances/:id/stop", post(controllers::instances::stop_instance))
        .route("/instances/:id/restart", post(controllers::instances::restart_instance))

        // User SSH Keys Management
        .route("/ssh-keys", get(controllers::vultr::list_ssh_keys))
        .route("/ssh-keys", post(controllers::vultr::create_ssh_key))
        .route("/ssh-keys/:id", get(controllers::vultr::get_ssh_key))
        .route("/ssh-keys/:id", patch(controllers::vultr::update_ssh_key))
        .route("/ssh-keys/:id", delete(controllers::vultr::delete_ssh_key))

        // User Hosting Services
        .route("/hosting/servers/vultr", get(controllers::intelligent_hosting::list_vultr_servers))
        .route("/hosting/servers/vultr/:instance_id", get(controllers::intelligent_hosting::get_server_details))
        .route("/hosting/servers/import/:instance_id", post(controllers::intelligent_hosting::import_server))
        .route("/hosting/servers/setup/:instance_id", get(controllers::intelligent_hosting::get_server_setup))
        .route("/hosting/deploy/user/:user_id", post(controllers::intelligent_hosting::deploy_user))
        .route("/hosting/infrastructure/status", get(controllers::intelligent_hosting::get_infrastructure_status))
        .route("/hosting/scaling/recommendations", get(controllers::intelligent_hosting::get_scaling_recommendations))
        .route("/hosting/scaling/execute", post(controllers::intelligent_hosting::execute_scaling_action))

        // Shared Hosting Multi-Tenant System
        .route("/shared-hosting/environments", post(controllers::shared_hosting::create_user_environment))
        .route("/shared-hosting/environments/:user_id", get(controllers::shared_hosting::get_user_environment))
        .route("/shared-hosting/environments/:user_id/state", put(controllers::shared_hosting::change_user_state))
        .route("/shared-hosting/environments/:user_id/wake", post(controllers::shared_hosting::wake_up_user))
        .route("/shared-hosting/environments/:user_id/sleep", post(controllers::shared_hosting::put_user_to_sleep))


}

fn admin_routes() -> Router<Arc<AppState>> {
    Router::new()
        // User Management (Admin)
        .route("/users", get(controllers::admin::list_users))
        .route("/users/:id", get(controllers::admin::get_user))
        .route("/users/:id/suspend", post(controllers::admin::suspend_user))
        .route("/users/:id/activate", post(controllers::admin::activate_user))

        // System Management (Admin)
        .route("/instances", get(controllers::admin::list_all_instances))
        .route("/billing/overview", get(controllers::admin::billing_overview))
        .route("/metrics", get(controllers::admin::get_metrics))
        .route("/analytics/profit", post(controllers::intelligent_hosting::calculate_profit_analysis))

        // Shared Hosting Management (Admin)
        .route("/shared-hosting/density", get(controllers::shared_hosting::get_server_density))
        .route("/shared-hosting/bulk-state-change", post(controllers::shared_hosting::bulk_change_state))
        .route("/shared-hosting/activity-monitor", get(controllers::shared_hosting::get_activity_monitor_status))
        .route("/shared-hosting/environments/:user_id/disable", post(controllers::shared_hosting::disable_user_access))

        // Vultr Infrastructure Management (Admin Only)
        .route("/vultr/plans", get(controllers::vultr::list_plans))
        .route("/vultr/plans-metal", get(controllers::vultr::list_metal_plans))
        .route("/vultr/regions", get(controllers::vultr::list_regions))
        .route("/vultr/regions/:region_id/availability", get(controllers::vultr::list_available_plans_region))
        .route("/vultr/os", get(controllers::vultr::list_os))
        .route("/vultr/backups", get(controllers::vultr::list_backups))

        // Vultr Dedicated Servers (Admin)
        .route("/vultr/servers", get(controllers::vultr::list_bare_metal))
        .route("/vultr/servers", post(controllers::vultr::create_bare_metal))
        .route("/vultr/servers/:id", get(controllers::vultr::get_bare_metal))
        .route("/vultr/servers/:id", put(controllers::vultr::update_bare_metal))
        .route("/vultr/servers/:id", delete(controllers::vultr::delete_bare_metal))
        .route("/vultr/servers/bare-metal/:id/ipv4", get(controllers::vultr::get_bare_metal_ipv4))
        .route("/vultr/servers/bare-metal/:id/ipv6", get(controllers::vultr::get_bare_metal_ipv6))
        .route("/vultr/servers/bare-metal/:id/bandwidth", get(controllers::vultr::get_bare_metal_bandwidth))
        .route("/vultr/servers/bare-metal/:id/user-data", get(controllers::vultr::get_bare_metal_user_data))
        .route("/vultr/servers/bare-metal/:id/upgrades", get(controllers::vultr::get_bare_metal_upgrades))
        .route("/vultr/servers/bare-metal/:id/vnc", get(controllers::vultr::get_bare_metal_vnc))

        // Vultr Bare Metal Operations (Admin)
        .route("/vultr/servers/bare-metal/:id/start", post(controllers::vultr::start_bare_metal))
        .route("/vultr/servers/bare-metal/:id/reboot", post(controllers::vultr::reboot_bare_metal))
        .route("/vultr/servers/bare-metal/:id/reinstall", post(controllers::vultr::reinstall_bare_metal))
        .route("/vultr/servers/bare-metal/:id/halt", post(controllers::vultr::halt_bare_metal))
        .route("/vultr/servers/bare-metal/halt", post(controllers::vultr::halt_bare_metals))
        .route("/vultr/servers/bare-metal/reboot", post(controllers::vultr::reboot_bare_metals))
        .route("/vultr/servers/bare-metal/start", post(controllers::vultr::start_bare_metals))
        .route("/vultr/servers/bare-metal/:id/vpcs", get(controllers::vultr::list_bare_metal_vpcs))
        .route("/vultr/servers/bare-metal/:id/vpcs/attach", post(controllers::vultr::attach_bare_metal_vpc))
        .route("/vultr/servers/bare-metal/:id/vpcs/detach", post(controllers::vultr::detach_bare_metal_vpc))

        // Vultr Block Storage (Admin)
        .route("/vultr/storage/blocks", get(controllers::vultr::list_block_storage))
        .route("/vultr/storage/blocks", post(controllers::vultr::create_block_storage))
        .route("/vultr/storage/blocks/:id", get(controllers::vultr::get_block_storage))
        .route("/vultr/storage/blocks/:id", put(controllers::vultr::update_block_storage))
        .route("/vultr/storage/blocks/:id", delete(controllers::vultr::delete_block_storage))
        .route("/vultr/storage/blocks/:id/attach", post(controllers::vultr::attach_block_storage))
        .route("/vultr/storage/blocks/:id/detach", post(controllers::vultr::detach_block_storage))

        // Vultr Billing (Admin)
        .route("/vultr/billing/invoice-items/:invoice_id", get(controllers::vultr::get_invoice_items))
        .route("/vultr/billing/pending-charges", get(controllers::vultr::get_pending_charges))

        // Vultr CDN Management (Admin)
        .route("/vultr/cdn/pull-zones", get(controllers::vultr::list_pull_zones))
        .route("/vultr/cdn/pull-zones", post(controllers::vultr::create_pull_zone))
        .route("/vultr/cdn/pull-zones/:id", get(controllers::vultr::get_pull_zone))
        .route("/vultr/cdn/pull-zones/:id", put(controllers::vultr::update_pull_zone))
        .route("/vultr/cdn/pull-zones/:id", delete(controllers::vultr::delete_pull_zone))
        .route("/vultr/cdn/pull-zones/:id/purge", post(controllers::vultr::purge_pull_zone))
        .route("/vultr/cdn/push-zones", get(controllers::vultr::list_push_zones))
        .route("/vultr/cdn/push-zones", post(controllers::vultr::create_push_zone))
        .route("/vultr/cdn/push-zones/:id", get(controllers::vultr::get_push_zone))
        .route("/vultr/cdn/push-zones/:id", put(controllers::vultr::update_push_zone))
        .route("/vultr/cdn/push-zones/:id", delete(controllers::vultr::delete_push_zone))
        .route("/vultr/cdn/push-zones/:id/files", get(controllers::vultr::get_push_zone_files))
        .route("/vultr/cdn/push-zones/:id/files/:file_name", delete(controllers::vultr::delete_push_zone_file))

        // Vultr Container Registry (Admin)
        .route("/vultr/registry", post(controllers::vultr::create_registry))
        .route("/vultr/registry/:id", put(controllers::vultr::update_registry))
        .route("/vultr/registry/:id", delete(controllers::vultr::delete_registry))
        .route("/vultr/registry/:id/replication", get(controllers::vultr::list_registry_replications))
        .route("/vultr/registry/:id/replication", post(controllers::vultr::create_registry_replication))
        .route("/vultr/registry/:id/replication/:replication_id", get(controllers::vultr::get_registry_replication))
        .route("/vultr/registry/:id/replication/:replication_id", delete(controllers::vultr::delete_registry_replication))
        .route("/vultr/registry/:id/repository", get(controllers::vultr::list_registry_repositories))
        .route("/vultr/registry/:id/repository/:repository_image", get(controllers::vultr::get_registry_repository))
        .route("/vultr/registry/:id/repository/:repository_image", put(controllers::vultr::update_registry_repository))
        .route("/vultr/registry/:id/repository/:repository_image", delete(controllers::vultr::delete_registry_repository))
        .route("/vultr/registry/:id/docker-credentials", post(controllers::vultr::create_registry_docker_credentials))
        .route("/vultr/registry/:id/kubernetes-docker-credentials", post(controllers::vultr::create_registry_kubernetes_docker_credentials))
        .route("/vultr/registry/:id/password", put(controllers::vultr::update_registry_password))
        .route("/vultr/registry/:id/robot", get(controllers::vultr::list_registry_robots))
        .route("/vultr/registry/:id/robot/:robot_name", get(controllers::vultr::get_registry_robot))
        .route("/vultr/registry/:id/robot/:robot_name", put(controllers::vultr::update_registry_robot))
        .route("/vultr/registry/:id/robot/:robot_name", delete(controllers::vultr::delete_registry_robot))
        .route("/vultr/registry/:id/repository/:repository_image/artifact", get(controllers::vultr::list_registry_repository_artifacts))
        .route("/vultr/registry/:id/repository/:repository_image/artifact/:artifact_digest", get(controllers::vultr::get_registry_repository_artifact))
        .route("/vultr/registry/:id/repository/:repository_image/artifact/:artifact_digest", delete(controllers::vultr::delete_registry_repository_artifact))
        .route("/vultr/registry/regions", get(controllers::vultr::list_registry_regions))
        .route("/vultr/registry/plans", get(controllers::vultr::list_registry_plans))

        // Vultr DNS Management (Admin)
        .route("/vultr/dns/domains", get(controllers::vultr::list_dns_domains))
        .route("/vultr/dns/domains", post(controllers::vultr::create_dns_domain))
        .route("/vultr/dns/domains/:domain", get(controllers::vultr::get_dns_domain))
        .route("/vultr/dns/domains/:domain", put(controllers::vultr::update_dns_domain))
        .route("/vultr/dns/domains/:domain", delete(controllers::vultr::delete_dns_domain))
        .route("/vultr/dns/domains/:domain/soa", get(controllers::vultr::get_dns_domain_soa))
        .route("/vultr/dns/domains/:domain/soa", patch(controllers::vultr::update_dns_domain_soa))
        .route("/vultr/dns/domains/:domain/dnssec", get(controllers::vultr::get_dns_domain_dnssec))
        .route("/vultr/dns/domains/:domain/records", get(controllers::vultr::list_dns_domain_records))
        .route("/vultr/dns/domains/:domain/records", post(controllers::vultr::create_dns_domain_record))
        .route("/vultr/dns/domains/:domain/records/:record_id", get(controllers::vultr::get_dns_domain_record))
        .route("/vultr/dns/domains/:domain/records/:record_id", patch(controllers::vultr::update_dns_domain_record))
        .route("/vultr/dns/domains/:domain/records/:record_id", delete(controllers::vultr::delete_dns_domain_record))

        // Vultr Firewall Management (Admin)
        .route("/vultr/firewalls", get(controllers::vultr::list_firewall_groups))
        .route("/vultr/firewalls", post(controllers::vultr::create_firewall_group))
        .route("/vultr/firewalls/:firewall_group_id", get(controllers::vultr::get_firewall_group))
        .route("/vultr/firewalls/:firewall_group_id", put(controllers::vultr::update_firewall_group))
        .route("/vultr/firewalls/:firewall_group_id", delete(controllers::vultr::delete_firewall_group))
        .route("/vultr/firewalls/:firewall_group_id/rules", get(controllers::vultr::list_firewall_group_rules))
        .route("/vultr/firewalls/:firewall_group_id/rules", post(controllers::vultr::create_firewall_group_rule))
        .route("/vultr/firewalls/:firewall_group_id/rules/:firewall_rule_id", get(controllers::vultr::get_firewall_group_rule))
        .route("/vultr/firewalls/:firewall_group_id/rules/:firewall_rule_id", delete(controllers::vultr::delete_firewall_group_rule))

        // Vultr Cloud Instance Management (Admin)
        .route("/vultr/cloud/instances", get(controllers::vultr::list_instances_detailed))
        .route("/vultr/cloud/instances", post(controllers::vultr::create_instance_detailed))
        .route("/vultr/cloud/instances/:instance_id", get(controllers::vultr::get_instance_detailed))
        .route("/vultr/cloud/instances/:instance_id", patch(controllers::vultr::update_instance_detailed))
        .route("/vultr/cloud/instances/:instance_id", delete(controllers::vultr::delete_instance_detailed))
        .route("/vultr/cloud/instances/halt", post(controllers::vultr::halt_instances))
        .route("/vultr/cloud/instances/reboot", post(controllers::vultr::reboot_instances))
        .route("/vultr/cloud/instances/start", post(controllers::vultr::start_instances))
        .route("/vultr/cloud/instances/:instance_id/halt", post(controllers::vultr::halt_instance))
        .route("/vultr/cloud/instances/:instance_id/reboot", post(controllers::vultr::reboot_instance))
        .route("/vultr/cloud/instances/:instance_id/start", post(controllers::vultr::start_instance))
        .route("/vultr/cloud/instances/:instance_id/reinstall", post(controllers::vultr::reinstall_instance))
        .route("/vultr/cloud/instances/:instance_id/bandwidth", get(controllers::vultr::get_instance_bandwidth))
        .route("/vultr/cloud/instances/:instance_id/neighbors", get(controllers::vultr::get_instance_neighbors))
        .route("/vultr/cloud/instances/:instance_id/vpcs", get(controllers::vultr::list_instance_vpcs))
        .route("/vultr/cloud/instances/:instance_id/iso", get(controllers::vultr::get_instance_iso_status))
        .route("/vultr/cloud/instances/:instance_id/iso/attach", post(controllers::vultr::attach_instance_iso))
        .route("/vultr/cloud/instances/:instance_id/iso/detach", post(controllers::vultr::detach_instance_iso))
        .route("/vultr/cloud/instances/:instance_id/vpcs/attach", post(controllers::vultr::attach_instance_vpc))
        .route("/vultr/cloud/instances/:instance_id/vpcs/detach", post(controllers::vultr::detach_instance_vpc))
        .route("/vultr/cloud/instances/:instance_id/backup-schedule", post(controllers::vultr::create_instance_backup_schedule))
        .route("/vultr/cloud/instances/:instance_id/backup-schedule", get(controllers::vultr::get_instance_backup_schedule))
        .route("/vultr/cloud/instances/:instance_id/restore", post(controllers::vultr::restore_instance))
        .route("/vultr/cloud/instances/:instance_id/ipv4", get(controllers::vultr::get_instance_ipv4))
        .route("/vultr/cloud/instances/:instance_id/ipv4", post(controllers::vultr::create_instance_ipv4))
        .route("/vultr/cloud/instances/:instance_id/ipv4/:ipv4", delete(controllers::vultr::delete_instance_ipv4))
        .route("/vultr/cloud/instances/:instance_id/ipv6", get(controllers::vultr::get_instance_ipv6))
        .route("/vultr/cloud/instances/:instance_id/ipv6/reverse", post(controllers::vultr::create_instance_reverse_ipv6))
        .route("/vultr/cloud/instances/:instance_id/ipv6/reverse", get(controllers::vultr::list_instance_ipv6_reverse))
        .route("/vultr/cloud/instances/:instance_id/ipv6/reverse/:ipv6", delete(controllers::vultr::delete_instance_reverse_ipv6))
        .route("/vultr/cloud/instances/:instance_id/ipv4/reverse", post(controllers::vultr::create_instance_reverse_ipv4))
        .route("/vultr/cloud/instances/:instance_id/ipv4/reverse/default/:ip", post(controllers::vultr::set_instance_default_reverse_ipv4))
        .route("/vultr/cloud/instances/:instance_id/user-data", get(controllers::vultr::get_instance_userdata))
        .route("/vultr/cloud/instances/:instance_id/upgrades", get(controllers::vultr::get_instance_upgrades))
        .route("/vultr/cloud/instances/:instance_id/jobs/:job_id", get(controllers::vultr::get_instance_job))

        // Vultr ISO Management (Admin)
        .route("/vultr/iso", get(controllers::vultr::list_isos))
        .route("/vultr/iso", post(controllers::vultr::create_iso))
        .route("/vultr/iso/:iso_id", get(controllers::vultr::get_iso))
        .route("/vultr/iso/:iso_id", delete(controllers::vultr::delete_iso))
        .route("/vultr/iso-public", get(controllers::vultr::list_public_isos))

        // Vultr Kubernetes Management (Admin)
        .route("/vultr/kubernetes/clusters", post(controllers::vultr::create_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters", get(controllers::vultr::list_kubernetes_clusters))
        .route("/vultr/kubernetes/clusters/:vke_id", get(controllers::vultr::get_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters/:vke_id", put(controllers::vultr::update_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters/:vke_id", delete(controllers::vultr::delete_kubernetes_cluster))
        .route("/vultr/kubernetes/clusters/:vke_id/delete-with-linked-resources", delete(controllers::vultr::delete_kubernetes_cluster_with_resources))
        .route("/vultr/kubernetes/clusters/:vke_id/resources", get(controllers::vultr::get_kubernetes_resources))
        .route("/vultr/kubernetes/clusters/:vke_id/available-upgrades", get(controllers::vultr::get_kubernetes_available_upgrades))
        .route("/vultr/kubernetes/clusters/:vke_id/upgrades", post(controllers::vultr::start_kubernetes_cluster_upgrade))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools", post(controllers::vultr::create_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools", get(controllers::vultr::get_nodepools))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", get(controllers::vultr::get_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", patch(controllers::vultr::update_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", delete(controllers::vultr::delete_nodepool))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id/nodes/:node_id", delete(controllers::vultr::delete_nodepool_instance))
        .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id/nodes/:node_id/recycle", post(controllers::vultr::recycle_nodepool_instance))
        .route("/vultr/kubernetes/clusters/:vke_id/config", get(controllers::vultr::get_kubernetes_cluster_config))
        .route("/vultr/kubernetes/versions", get(controllers::vultr::get_kubernetes_versions))

        // Vultr Load Balancer Management (Admin)
        .route("/vultr/load-balancers", get(controllers::vultr::list_load_balancers))
        .route("/vultr/load-balancers", post(controllers::vultr::create_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id", get(controllers::vultr::get_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id", patch(controllers::vultr::update_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id", delete(controllers::vultr::delete_load_balancer))
        .route("/vultr/load-balancers/:load_balancer_id/ssl", delete(controllers::vultr::delete_load_balancer_ssl))
        .route("/vultr/load-balancers/:load_balancer_id/ssl/auto", delete(controllers::vultr::delete_load_balancer_auto_ssl))
        .route("/vultr/load-balancers/:load_balancer_id/forwarding-rules", post(controllers::vultr::create_load_balancer_forwarding_rule))
        .route("/vultr/load-balancers/:load_balancer_id/forwarding-rules/:forwarding_rule_id", get(controllers::vultr::get_load_balancer_forwarding_rule))
        .route("/vultr/load-balancers/:load_balancer_id/forwarding-rules/:forwarding_rule_id", delete(controllers::vultr::delete_load_balancer_forwarding_rule))
        .route("/vultr/load-balancers/:load_balancer_id/firewall-rules", get(controllers::vultr::list_load_balancer_firewall_rules))
        .route("/vultr/load-balancers/:load_balancer_id/firewall-rules/:firewall_rule_id", get(controllers::vultr::get_load_balancer_firewall_rule))

        // Vultr Managed Database (Admin)
        .route("/vultr/databases/plans", get(controllers::vultr::list_database_plans))
        .route("/vultr/databases", get(controllers::vultr::list_managed_databases))
        .route("/vultr/databases", post(controllers::vultr::create_managed_database))
        .route("/vultr/databases/:database_id", get(controllers::vultr::get_managed_database))
        .route("/vultr/databases/:database_id", put(controllers::vultr::update_managed_database))
        .route("/vultr/databases/:database_id", delete(controllers::vultr::delete_managed_database))
        .route("/vultr/databases/:database_id/usage", get(controllers::vultr::get_database_usage))
        .route("/vultr/databases/:database_id/users", get(controllers::vultr::list_database_users))
        .route("/vultr/databases/:database_id/users", post(controllers::vultr::create_database_user))
        .route("/vultr/databases/:database_id/users/:username", get(controllers::vultr::get_database_user))
        .route("/vultr/databases/:database_id/users/:username", put(controllers::vultr::update_database_user))
        .route("/vultr/databases/:database_id/users/:username", delete(controllers::vultr::delete_database_user))
        .route("/vultr/databases/:database_id/users/:username/access-control", put(controllers::vultr::set_database_user_acl))

        // Additional Vultr Services (Admin)
        .route("/vultr/marketplace/apps/:image_id/variables", get(controllers::vultr::list_marketplace_app_variables))
        .route("/vultr/object-storage", get(controllers::vultr::list_object_storages))
        .route("/vultr/object-storage", post(controllers::vultr::create_object_storage))
        .route("/vultr/object-storage/:object_storage_id", get(controllers::vultr::get_object_storage))
        .route("/vultr/object-storage/:object_storage_id", delete(controllers::vultr::delete_object_storage))
        .route("/vultr/object-storage/:object_storage_id", put(controllers::vultr::update_object_storage))
        .route("/vultr/object-storage/:object_storage_id/regenerate-keys", post(controllers::vultr::regenerate_object_storage_keys))
        .route("/vultr/object-storage/clusters", get(controllers::vultr::list_object_storage_clusters))
        .route("/vultr/object-storage/tiers", get(controllers::vultr::list_object_storage_tiers))
        .route("/vultr/object-storage/clusters/:cluster_id/tiers", get(controllers::vultr::list_object_storage_cluster_tiers))
        .route("/vultr/inference", get(controllers::vultr::list_inference))
        .route("/vultr/inference", post(controllers::vultr::create_inference))
        .route("/vultr/inference/:inference_id", get(controllers::vultr::get_inference))
        .route("/vultr/inference/:inference_id", patch(controllers::vultr::update_inference))
        .route("/vultr/inference/:inference_id", delete(controllers::vultr::delete_inference))
        .route("/vultr/inference/:inference_id/usage", get(controllers::vultr::get_inference_usage))
        .route("/vultr/vpcs", get(controllers::vultr::list_vpcs))
        .route("/vultr/vpcs", post(controllers::vultr::create_vpc))
        .route("/vultr/vpcs/:vpc_id", get(controllers::vultr::get_vpc))
        .route("/vultr/vpcs/:vpc_id", put(controllers::vultr::update_vpc))
        .route("/vultr/vpcs/:vpc_id", delete(controllers::vultr::delete_vpc))
        .route("/vultr/reserved-ips", get(controllers::vultr::list_reserved_ips))
        .route("/vultr/reserved-ips", post(controllers::vultr::create_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip", get(controllers::vultr::get_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip", patch(controllers::vultr::update_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip", delete(controllers::vultr::delete_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip/attach", post(controllers::vultr::attach_reserved_ip))
        .route("/vultr/reserved-ips/:reserved_ip/detach", post(controllers::vultr::detach_reserved_ip))
        .route("/vultr/reserved-ips/convert", post(controllers::vultr::convert_reserved_ip))
        .route("/vultr/snapshots", get(controllers::vultr::list_snapshots))
        .route("/vultr/snapshots", post(controllers::vultr::create_snapshot))
        .route("/vultr/snapshots/create-from-url", post(controllers::vultr::create_snapshot_from_url))
        .route("/vultr/snapshots/:snapshot_id", get(controllers::vultr::get_snapshot))
        .route("/vultr/snapshots/:snapshot_id", put(controllers::vultr::update_snapshot))
        .route("/vultr/snapshots/:snapshot_id", delete(controllers::vultr::delete_snapshot))
        .route("/vultr/subaccounts", get(controllers::vultr::list_subaccounts))
        .route("/vultr/subaccounts", post(controllers::vultr::create_subaccount))
        .route("/vultr/ssh-keys", get(controllers::vultr::list_ssh_keys))
        .route("/vultr/ssh-keys", post(controllers::vultr::create_ssh_key))
        .route("/vultr/ssh-keys/:ssh_key_id", get(controllers::vultr::get_ssh_key))
        .route("/vultr/ssh-keys/:ssh_key_id", patch(controllers::vultr::update_ssh_key))
        .route("/vultr/ssh-keys/:ssh_key_id", delete(controllers::vultr::delete_ssh_key))
        .route("/vultr/startup-scripts", get(controllers::vultr::list_startup_scripts))
        .route("/vultr/startup-scripts", post(controllers::vultr::create_startup_script))
        .route("/vultr/startup-scripts/:script_id", get(controllers::vultr::get_startup_script))
        .route("/vultr/startup-scripts/:script_id", patch(controllers::vultr::update_startup_script))
        .route("/vultr/startup-scripts/:script_id", delete(controllers::vultr::delete_startup_script))
        .route("/vultr/storage-gateways", get(controllers::vultr::list_storage_gateways))
        .route("/vultr/storage-gateways", post(controllers::vultr::create_storage_gateway))
        .route("/vultr/storage-gateways/:gateway_id", get(controllers::vultr::get_storage_gateway))
        .route("/vultr/storage-gateways/:gateway_id", put(controllers::vultr::update_storage_gateway))
        .route("/vultr/storage-gateways/:gateway_id", delete(controllers::vultr::delete_storage_gateway))
        .route("/vultr/users", get(controllers::vultr::list_users))
        .route("/vultr/users", post(controllers::vultr::create_user))
        .route("/vultr/users/:user_id", get(controllers::vultr::get_user))
        .route("/vultr/users/:user_id", patch(controllers::vultr::update_user))
        .route("/vultr/users/:user_id", delete(controllers::vultr::delete_user))
        .route("/vultr/vfs/regions", get(controllers::vultr::list_vfs_regions))
        .route("/vultr/vfs", get(controllers::vultr::list_vfs))
        .route("/vultr/vfs", post(controllers::vultr::create_vfs))
        .route("/vultr/vfs/:vfs_id", get(controllers::vultr::get_vfs))
        .route("/vultr/vfs/:vfs_id", put(controllers::vultr::update_vfs))
        .route("/vultr/vfs/:vfs_id", delete(controllers::vultr::delete_vfs))
}

async fn handle_404() -> impl IntoResponse {
    let response = ApiResponse::<()> {
        success: false,
        data: None,
        message: Some("Endpoint not found".to_string()),
        error: Some("The requested endpoint does not exist".to_string()),
        metadata: Some(ResponseMetadata {
            timestamp: chrono::Utc::now(),
            request_id: Some(uuid::Uuid::new_v4().to_string()),
            version: "v1".to_string(),
            pagination: None,
        }),
    };

    (StatusCode::NOT_FOUND, Json(response))
}
