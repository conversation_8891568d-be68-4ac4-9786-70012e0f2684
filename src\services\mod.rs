pub mod admin;
pub mod auth;
pub mod billing;
pub mod blueprint;
pub mod build;
pub mod deployment;
pub mod disk;
pub mod domain;
pub mod environment;
pub mod git;
pub mod hosting_plans;
pub mod instance;
pub mod intelligent_hosting;
pub mod kubernetes_deployment;
pub mod shared_hosting;
pub mod shared_hosting_orchestrator;
pub mod state_management;
pub mod ssh_key_management;
pub mod resource_allocation;
pub mod load_balancing;
pub mod activity_monitoring;
pub mod payment_access_control;
pub mod user;

use crate::controllers::ControllerError;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ServiceError {
    #[error("Database error: {0}")]
    Database(#[from] mongodb::error::Error),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Authorization error: {0}")]
    Authorization(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Conflict: {0}")]
    Conflict(String),
    
    #[error("ExtAPI error: {0}")]
    ExternalApi(String),
    
    #[error("Internal error: {0}")]
    Internal(String),

    #[error("Bad request: {0}")]
    BadRequest(String),
}


pub type ServiceResult<T> = Result<T, ServiceError>;
