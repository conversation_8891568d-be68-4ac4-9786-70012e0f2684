import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  MagnifyingGlassIcon,
  ServerIcon,
  CheckCircleIcon,
  PauseIcon,
  ClockIcon,
  XCircleIcon,
  GlobeAltIcon,
  RocketLaunchIcon,
  ComputerDesktopIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';
import { applicationApi, type Application } from '../../services/api';
import { useApiCall } from '../../hooks/useApiCall';

// Helper function to format deployment time
const formatDeploymentTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    return diffInHours === 0 ? 'Just now' : `${diffInHours}h`;
  }
  return `${diffInDays}d`;
};

export default function Services() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Fetch applications from API
  const { data: applications, loading, error, refetch } = useApiCall({
    key: 'applications-list',
    apiCall: applicationApi.list,
    dependencies: [],
    autoFetch: true,
  });

  const servicesData = applications || [];

  // Filter services based on search term and status
  const filteredServices = servicesData.filter((service: Application) => {
    const matchesSearch =
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (service.runtime_config.service_type && service.runtime_config.service_type.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (service.runtime_config.region && service.runtime_config.region.toLowerCase().includes(searchTerm.toLowerCase())) ||
      // Legacy support
      (service.runtime && service.runtime.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (service.region && service.region.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'all' ||
                          (statusFilter === 'active' && (service.status === 'Running' || service.status === 'deployed')) ||
                          (statusFilter === 'suspended' && (service.status === 'Suspended' || service.status === 'Stopped' || service.status === 'suspended' || service.status === 'stopped'));

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Running':
      case 'deployed':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckCircleIcon className="mr-1 h-3 w-3 text-green-400" />
            Running
          </span>
        );
      case 'Suspended':
      case 'Stopped':
      case 'suspended':
      case 'stopped':
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            <PauseIcon className="h-3 w-3 mr-1 text-gray-400" />
            Stopped
          </span>
        );
      case 'Creating':
      case 'Building':
      case 'Deploying':
      case 'building':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-900/30 border border-yellow-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-yellow-300">
            <ClockIcon className="h-3 w-3 mr-1 text-yellow-400" />
            {status === 'Creating' ? 'Creating' : status === 'Building' ? 'Building' : status === 'Deploying' ? 'Deploying' : 'Building'}
          </span>
        );
      case 'Failed':
      case 'failed':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XCircleIcon className="h-3 w-3 mr-1 text-red-400" />
            Failed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
            <ClockIcon className="h-3 w-3 mr-1 text-blue-400" />
            {status}
          </span>
        );
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">Services</h1>
            <p className="mt-1 text-sm text-gray-400">Manage your deployed services and applications</p>
          </div>
          <Button
            variant="glass"
            glow={true}
            as={Link}
            to="/dashboard/services/create"
            icon={<ServerIcon className="h-5 w-5" />}
          >
            New Service
          </Button>
        </div>
        <Card className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-secondary-500"></div>
            <span className="ml-3 text-gray-400">Loading services...</span>
          </div>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">Services</h1>
            <p className="mt-1 text-sm text-gray-400">Manage your deployed services and applications</p>
          </div>
          <Button
            variant="glass"
            glow={true}
            as={Link}
            to="/dashboard/services/create"
            icon={<ServerIcon className="h-5 w-5" />}
          >
            New Service
          </Button>
        </div>
        <Card className="p-8">
          <div className="text-center">
            <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-medium text-white">Error loading services</h3>
            <p className="mt-1 text-sm text-gray-400">{error.message}</p>
            <div className="mt-6">
              <Button variant="outline" onClick={() => refetch()}>
                Try again
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Services</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your deployed services and applications</p>
        </div>
        <Button
          variant="glass"
          glow={true}
          as={Link}
          to="/dashboard/services/create"
          icon={<ServerIcon className="h-5 w-5" />}
        >
          New Service
        </Button>
      </div>

      {/* Hosting Services Section */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h2 className="text-xl font-semibold text-white">Hosting Services</h2>
            <p className="mt-1 text-sm text-gray-400">Manage your web hosting applications</p>
          </div>
          <Button
            variant="glass"
            size="sm"
            as={Link}
            to="/dashboard/hosting"
            icon={<ArrowRightIcon className="h-5 w-5" />}
          >
            View All Hosting
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="p-5 relative overflow-hidden border border-white/10 hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg hover:shadow-secondary-500/10">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center">
                <ComputerDesktopIcon className="h-8 w-8 text-secondary-500 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Web Hosting</h3>
                  <p className="text-sm text-gray-400">Deploy websites and web applications</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant="glass"
                size="sm"
                as={Link}
                to="/dashboard/hosting"
                fullWidth
              >
                Manage Hosting
              </Button>
            </div>
          </Card>

          <Card className="p-5 relative overflow-hidden border border-white/10 hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg hover:shadow-secondary-500/10">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center">
                <ServerIcon className="h-8 w-8 text-secondary-500 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Databases</h3>
                  <p className="text-sm text-gray-400">Manage database instances</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant="glass"
                size="sm"
                as={Link}
                to="/dashboard/hosting"
                fullWidth
              >
                Manage Databases
              </Button>
            </div>
          </Card>

          <Card className="p-5 relative overflow-hidden border border-white/10 hover:border-secondary-500/50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg hover:shadow-secondary-500/10">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center">
                <RocketLaunchIcon className="h-8 w-8 text-secondary-500 mr-3" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Deploy New</h3>
                  <p className="text-sm text-gray-400">Create a new hosting service</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button
                variant="glass"
                size="sm"
                as={Link}
                to="/dashboard/services/create"
                fullWidth
              >
                Deploy Now
              </Button>
            </div>
          </Card>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div className="relative flex-grow max-w-md">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              placeholder="Search services"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex space-x-2">
            <Button
              variant={statusFilter === 'all' ? 'glass' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('all')}
            >
              All ({servicesData.length})
            </Button>
            <Button
              variant={statusFilter === 'active' ? 'glass' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('active')}
            >
              Active ({servicesData.filter((s: Application) => s.status === 'Running' || s.status === 'deployed').length})
            </Button>
            <Button
              variant={statusFilter === 'suspended' ? 'glass' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('suspended')}
            >
              Stopped ({servicesData.filter((s: Application) => s.status === 'Suspended' || s.status === 'Stopped' || s.status === 'suspended' || s.status === 'stopped').length})
            </Button>
          </div>
        </div>

        <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
          <table className="min-w-full divide-y divide-white/10">
            <thead>
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                  Service Name
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Status
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Service Type
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Region
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Plan
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Last Updated
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {filteredServices.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-14 text-center text-sm text-gray-400">
                    {servicesData.length === 0 ? (
                      <div>
                        <ServerIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-white">No services</h3>
                        <p className="mt-1 text-sm text-gray-400">Get started by creating a new service.</p>
                        <div className="mt-6">
                          <Button
                            variant="glass"
                            as={Link}
                            to="/dashboard/services/create"
                            icon={<ServerIcon className="h-4 w-4" />}
                          >
                            Create Service
                          </Button>
                        </div>
                      </div>
                    ) : (
                      'No services match your search criteria.'
                    )}
                  </td>
                </tr>
              ) : (
                filteredServices.map((service: Application) => (
                  <tr key={service.id} className="hover:bg-white/5 transition-colors duration-200">
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                      <Link to={`/dashboard/services/${service.id}`} className="flex items-center hover:text-secondary-400 transition-colors duration-200">
                        <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-2" />
                        <div>
                          <div className="font-medium">{service.name}</div>
                          {service.description && (
                            <div className="text-xs text-gray-400 mt-1">{service.description}</div>
                          )}
                        </div>
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {getStatusBadge(service.status)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {service.runtime_config?.service_type || service.runtime || 'N/A'}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {service.runtime_config?.region || service.region || 'N/A'}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {service.runtime_config?.plan || 'N/A'}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {formatDeploymentTime(service.updated_at)}
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div className="flex justify-end space-x-2">
                        {service.url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(service.url, '_blank', 'noopener,noreferrer')}
                          >
                            Open
                          </Button>
                        )}
                        <Button
                          variant="glass"
                          size="sm"
                          as={Link}
                          to={`/dashboard/services/${service.id}`}
                        >
                          Manage
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}
