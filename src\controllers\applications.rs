use crate::{
    controllers::{<PERSON><PERSON><PERSON><PERSON>, ControllerResult},
    models::{
        ApiResponse, CreateApplicationRequest, ApplicationResponse,
        TriggerDeploymentRequest, DeploymentResponse, PaginationQuery, Claims,
        deployment::{
            CreateSimpleApplicationRequest, SimpleServiceType, HostingTier,
            CreateRepositoryRequest, CreateEnvironmentRequest, CreateRuntimeConfigRequest,
            ServiceType, GitProvider, AutoScalingConfig, HealthCheckConfig, NetworkingConfig
        }
    },
    services::{deployment::DeploymentService, hosting_plans::HostingPlansService},
    AppState,
};
use axum::{
    extract::{Path, Query, State},
    response::Json,
};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state, claims, request))]
pub async fn create_application(
    State(state): State<Arc<AppState>>,
    claims: <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>(request): Json<CreateApplicationRequest>,
) -> ControllerResult<Json<ApiResponse<ApplicationResponse>>> {
    // Inline validation checks
    request.validate().map_err(|e| {
        ControllerError::Validation(format!("application data is invalid: {}", e))
    })?;

    let name = request.name.as_str();

    if name.is_empty() {
        return Err(ControllerError::Validation("name is required".to_string()));
    }

    if name.len() > 100 {
        return Err(ControllerError::Validation("name must be less than 100 characters".to_string()));
    }

    // Validate repository URL
    let repository = &request.repository;
    if repository.url.is_empty() {
        return Err(ControllerError::Validation("repository URL is required".to_string()));
    }

    if repository.branch.is_empty() {
        return Err(ControllerError::Validation("repository branch is required".to_string()));
    }

    // Validate environment
    let environment = &request.environment;
    if environment.name.is_empty() {
        return Err(ControllerError::Validation("environment name is required".to_string()));
    }

    if environment.start_command.is_empty() {
        return Err(ControllerError::Validation("start command is required".to_string()));
    }

    // Validate runtime config
    let runtime_config = &request.runtime_config;
    if runtime_config.instance_type.is_empty() {
        return Err(ControllerError::Validation("instance type is required".to_string()));
    }

    if runtime_config.region.is_empty() {
        return Err(ControllerError::Validation("region is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let application = deployment_service
        .create_application(&claims.sub, request)
        .await
        .map_err(ControllerError::from)?;

    Ok(Json(ApiResponse::success(application)))
}

#[instrument(skip(state, claims))]
pub async fn list_applications(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Query(pagination): Query<PaginationQuery>,
) -> ControllerResult<Json<ApiResponse<Vec<ApplicationResponse>>>> {
    pagination.validate().map_err(|e| {
        ControllerError::Validation(format!("pagination parameters are invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let applications = deployment_service
        .list_applications(&claims.sub, pagination)
        .await
        .map_err(ControllerError::from)?;

    Ok(Json(ApiResponse::success(applications)))
}

#[instrument(skip(state, claims, app_id))]
pub async fn get_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<ApplicationResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let application = deployment_service
        .get_application(&claims.sub, &app_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::from(e),
        })?;

    Ok(Json(ApiResponse::success(application)))
}

#[instrument(skip(state, claims, app_id, request))]
pub async fn update_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
    Json(request): Json<CreateApplicationRequest>,
) -> ControllerResult<Json<ApiResponse<ApplicationResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    request.validate().map_err(|e| {
        ControllerError::Validation(format!("application data is invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let application = deployment_service
        .update_application(&claims.sub, &app_id, request)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::from(e),
        })?;

    Ok(Json(ApiResponse::success(application)))
}

#[instrument(skip(state, claims, app_id))]
pub async fn delete_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<()>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    deployment_service
        .delete_application(&claims.sub, &app_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::from(e),
        })?;

    Ok(Json(ApiResponse::success(())))
}

#[instrument(skip(state, claims, app_id, request))]
pub async fn trigger_deployment(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
    Json(request): Json<TriggerDeploymentRequest>,
) -> ControllerResult<Json<ApiResponse<DeploymentResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    request.validate().map_err(|e| {
        ControllerError::Validation(format!("deployment request is invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployment = deployment_service
        .trigger_deployment(&claims.sub, &app_id, request)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::from(e),
        })?;

    Ok(Json(ApiResponse::success(deployment)))
}

#[instrument(skip(state, claims, app_id))]
pub async fn list_deployments(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path(app_id): Path<String>,
    Query(pagination): Query<PaginationQuery>,
) -> ControllerResult<Json<ApiResponse<Vec<DeploymentResponse>>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    pagination.validate().map_err(|e| {
        ControllerError::Validation(format!("pagination parameters are invalid: {}", e))
    })?;

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployments = deployment_service
        .list_deployments(&claims.sub, &app_id, pagination)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Application not found".to_string()),
            _ => ControllerError::from(e),
        })?;

    Ok(Json(ApiResponse::success(deployments)))
}

#[instrument(skip(state, claims, app_id, deployment_id))]
pub async fn get_deployment(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path((app_id, deployment_id)): Path<(String, String)>,
) -> ControllerResult<Json<ApiResponse<DeploymentResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    if deployment_id.is_empty() {
        return Err(ControllerError::Validation("deployment_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployment = deployment_service
        .get_deployment(&claims.sub, &app_id, &deployment_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Deployment not found".to_string()),
            _ => ControllerError::from(e),
        })?;

    Ok(Json(ApiResponse::success(deployment)))
}

#[instrument(skip(state, claims, app_id, deployment_id))]
pub async fn rollback_deployment(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Path((app_id, deployment_id)): Path<(String, String)>,
) -> ControllerResult<Json<ApiResponse<DeploymentResponse>>> {
    // Inline validation checks
    if app_id.is_empty() {
        return Err(ControllerError::Validation("application_id is required".to_string()));
    }

    if deployment_id.is_empty() {
        return Err(ControllerError::Validation("deployment_id is required".to_string()));
    }

    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);
    
    let deployment = deployment_service
        .rollback_deployment(&claims.sub, &app_id, &deployment_id)
        .await
        .map_err(|e| match e {
            crate::services::ServiceError::NotFound(_) => ControllerError::NotFound("Deployment not found".to_string()),
            _ => ControllerError::from(e),
        })?;

    Ok(Json(ApiResponse::success(deployment)))
}

// Simplified application creation for easy sharing of Vultr servers
#[instrument(skip(state, claims, request))]
pub async fn create_simple_application(
    State(state): State<Arc<AppState>>,
    claims: Claims,
    Json(request): Json<CreateSimpleApplicationRequest>,
) -> ControllerResult<Json<ApiResponse<ApplicationResponse>>> {
    // Add detailed logging
    tracing::info!(
        "Creating simple application for user: {} with request: {:?}",
        claims.sub,
        request
    );

    // Validate the simplified request
    if let Err(validation_errors) = request.validate() {
        tracing::error!("Validation failed: {:?}", validation_errors);
        return Err(ControllerError::Validation(format!("application data is invalid: {}", validation_errors)));
    }

    // Basic validation with detailed logging
    if request.name.is_empty() {
        tracing::error!("Application name is empty");
        return Err(ControllerError::Validation("name is required".to_string()));
    }

    // Determine deployment mode
    let deployment_mode = request.deployment_mode.as_ref()
        .unwrap_or(&crate::models::deployment::DeploymentMode::Git);
    tracing::info!("Deployment mode: {:?}", deployment_mode);

    // Validate based on deployment mode
    match deployment_mode {
        crate::models::deployment::DeploymentMode::Git => {
            if request.repository_url.is_none() || request.repository_url.as_ref().unwrap().is_empty() {
                tracing::error!("Git deployment mode requires repository URL");
                return Err(ControllerError::Validation("repository URL is required for Git deployment".to_string()));
            }
            tracing::info!("Git mode - using repository: {}", request.repository_url.as_ref().unwrap());
        },
        crate::models::deployment::DeploymentMode::Docker => {
            if request.docker_image.is_none() || request.docker_image.as_ref().unwrap().is_empty() {
                tracing::error!("Docker deployment mode requires docker image");
                return Err(ControllerError::Validation("docker image is required for Docker deployment".to_string()));
            }
            tracing::info!("Docker mode - using image: {}", request.docker_image.as_ref().unwrap());
        },
        crate::models::deployment::DeploymentMode::Upload => {
            tracing::info!("Upload mode - files will be uploaded separately");
        }
    }

    // Convert simplified request to full request with smart defaults
    let full_request = convert_simple_to_full_request(request)?;

    // Use the existing deployment service
    let deployment_service = DeploymentService::new(&state.database, &state.vultr_client, &state.config);

    let application = deployment_service
        .create_application(&claims.sub, full_request)
        .await
        .map_err(ControllerError::from)?;

    Ok(Json(ApiResponse::success(application)))
}

// Helper function to convert simplified request to full request with smart defaults
fn convert_simple_to_full_request(
    simple: CreateSimpleApplicationRequest,
) -> Result<CreateApplicationRequest, ControllerError> {
    let hosting_plans_service = HostingPlansService::new();

    // Handle repository URL based on deployment mode
    let deployment_mode = simple.deployment_mode.as_ref()
        .unwrap_or(&crate::models::deployment::DeploymentMode::Git);

    let (repository_url, git_provider) = match deployment_mode {
        crate::models::deployment::DeploymentMode::Git => {
            let repo_url = simple.repository_url.as_ref()
                .ok_or_else(|| ControllerError::Validation("repository URL is required for Git deployment".to_string()))?;

            let provider = if repo_url.contains("github.com") {
                GitProvider::GitHub
            } else if repo_url.contains("gitlab.com") {
                GitProvider::GitLab
            } else if repo_url.contains("bitbucket.org") {
                GitProvider::Bitbucket
            } else {
                GitProvider::GitHub // Default to GitHub
            };
            (repo_url.clone(), provider)
        },
        crate::models::deployment::DeploymentMode::Upload => {
            // For upload sources, use a placeholder repository
            ("https://github.com/placeholder/upload-source".to_string(), GitProvider::GitHub)
        },
        crate::models::deployment::DeploymentMode::Docker => {
            // For docker sources, use a placeholder repository
            ("https://github.com/placeholder/docker-source".to_string(), GitProvider::GitHub)
        }
    };

    // Convert service type to runtime configuration
    let (service_type, default_build_cmd, default_start_cmd, default_port) = match simple.service_type {
        SimpleServiceType::StaticWebsite => (
            ServiceType::StaticSite,
            Some("npm run build".to_string()),
            "serve -s build -l 3000".to_string(),
            3000,
        ),
        SimpleServiceType::NodeJsApp => (
            ServiceType::WebService,
            Some("npm install && npm run build".to_string()),
            "npm start".to_string(),
            3000,
        ),
        SimpleServiceType::PythonApp => (
            ServiceType::WebService,
            Some("pip install -r requirements.txt".to_string()),
            "python app.py".to_string(),
            5000,
        ),
        SimpleServiceType::PhpApp => (
            ServiceType::WebService,
            Some("composer install".to_string()),
            "php -S 0.0.0.0:8000".to_string(),
            8000,
        ),
        SimpleServiceType::ReactApp => (
            ServiceType::StaticSite,
            Some("npm install && npm run build".to_string()),
            "serve -s build -l 3000".to_string(),
            3000,
        ),
        SimpleServiceType::VueApp => (
            ServiceType::StaticSite,
            Some("npm install && npm run build".to_string()),
            "serve -s dist -l 3000".to_string(),
            3000,
        ),
        SimpleServiceType::NextJsApp => (
            ServiceType::WebService,
            Some("npm install && npm run build".to_string()),
            "npm start".to_string(),
            3000,
        ),
        SimpleServiceType::DockerApp => (
            ServiceType::WebService,
            None,
            "docker run -p 3000:3000 app".to_string(),
            3000,
        ),
    };

    // Get the hosting plan based on tier and custom plan name
    let hosting_plan = if let Some(plan_name) = &simple.custom_plan {
        hosting_plans_service.get_plan_by_name(plan_name)
            .map_err(|e| ControllerError::Internal(format!("Failed to get hosting plan: {}", e)))?
            .ok_or_else(|| ControllerError::Validation(format!("Invalid plan name: {}", plan_name)))?
    } else {
        hosting_plans_service.get_default_plan(&simple.hosting_tier)
            .map_err(|e| ControllerError::Internal(format!("Failed to get default plan: {}", e)))?
    };

    // Set smart defaults based on hosting tier
    let branch = simple.branch.unwrap_or_else(|| "main".to_string());
    let region = simple.region.unwrap_or_else(|| "ng-lag".to_string()); // Default to Lagos, Nigeria

    // For Dedicated/Enterprise, use Vultr plan; for Shared, use your allocated servers
    let instance_type = match simple.hosting_tier {
        HostingTier::Shared => "shared-pool".to_string(), // Special identifier for shared hosting
        HostingTier::Dedicated | HostingTier::Enterprise => {
            simple.vultr_plan.or(hosting_plan.vultr_plan.clone())
                .unwrap_or_else(|| "vc2-1c-1gb".to_string()) // Fallback to smallest plan
        }
    };

    let build_command = simple.build_command.or(default_build_cmd);
    let start_command = simple.start_command.unwrap_or(default_start_cmd);

    let mut environment_vars = simple.environment_variables.unwrap_or_default();

    // Add default environment variables based on service type
    match simple.service_type {
        SimpleServiceType::NodeJsApp | SimpleServiceType::NextJsApp => {
            environment_vars.insert("NODE_ENV".to_string(), "production".to_string());
            environment_vars.insert("PORT".to_string(), default_port.to_string());
        },
        SimpleServiceType::PythonApp => {
            environment_vars.insert("FLASK_ENV".to_string(), "production".to_string());
            environment_vars.insert("PORT".to_string(), default_port.to_string());
        },
        SimpleServiceType::PhpApp => {
            environment_vars.insert("APP_ENV".to_string(), "production".to_string());
        },
        _ => {}
    }

    Ok(CreateApplicationRequest {
        name: simple.name,
        description: simple.description,
        repository: CreateRepositoryRequest {
            provider: git_provider,
            url: repository_url,
            branch,
            auto_deploy: true, // Enable auto-deploy by default
            access_token: None, // Users can add this later if needed
        },
        environment: CreateEnvironmentRequest {
            name: "production".to_string(),
            variables: environment_vars,
            secrets: HashMap::new(), // Users can add secrets later
            build_command,
            start_command,
            dockerfile_path: None, // Auto-detect
            root_directory: Some(".".to_string()),
        },
        runtime_config: CreateRuntimeConfigRequest {
            service_type,
            instance_type,
            region,
            auto_scaling: AutoScalingConfig {
                enabled: true,
                min_instances: 1,
                max_instances: 3,
                target_cpu_percent: 70.0,
                target_memory_percent: 80.0,
                scale_up_cooldown: 300,
                scale_down_cooldown: 600,
            },
            health_check: HealthCheckConfig {
                enabled: true,
                path: "/health".to_string(),
                port: default_port,
                interval: 30,
                timeout: 10,
                retries: 3,
                grace_period: 60,
            },
            networking: NetworkingConfig {
                port: default_port,
                custom_domains: Vec::new(), // Users can add custom domain later
                ssl_enabled: true,
                internal_networking: false,
            },
        },
    })
}
