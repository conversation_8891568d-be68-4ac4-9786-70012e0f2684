use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// User state in the multi-tenant shared hosting system
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum UserState {
    /// Active paying users with full resource access
    Hot,
    /// Inactive or non-paying users with disabled access
    Cold,
    /// Users with no activity - hibernated with minimal resources
    Sleep,
}

/// SSH key information for dual-key authentication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSSHKey {
    pub user_id: String,
    pub public_key: String,
    pub key_fingerprint: String,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub is_active: bool,
}

/// User environment state tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserEnvironmentState {
    pub user_id: String,
    pub current_state: UserState,
    pub previous_state: Option<UserState>,
    pub plan: String,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub last_state_change: Option<DateTime<Utc>>,
    pub payment_status: PaymentStatus,
    pub resource_usage: UserResourceUsage,
    pub directory_path: String,
    pub quota_limit: String,
    pub container_id: Option<String>,
}

/// Payment status for state management
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PaymentStatus {
    Active,
    Overdue,
    Suspended,
    Trial,
}

/// Resource usage tracking for individual users
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserResourceUsage {
    pub cpu_usage_percent: f64,
    pub memory_usage_mb: u64,
    pub storage_usage_mb: u64,
    pub bandwidth_usage_mb: u64,
    pub request_count: u64,
    pub last_updated: DateTime<Utc>,
}

/// Server density optimization data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerDensityInfo {
    pub server_id: String,
    pub total_users: u32,
    pub hot_users: u32,
    pub cold_users: u32,
    pub sleep_users: u32,
    pub available_slots: u32,
    pub cpu_utilization: f64,
    pub memory_utilization: f64,
    pub storage_utilization: f64,
    pub density_score: f64, // Algorithm-calculated efficiency score
}

/// Load balancing decision data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadBalancingDecision {
    pub user_id: String,
    pub target_server_id: String,
    pub reason: String,
    pub migration_required: bool,
    pub estimated_migration_time: Option<u32>, // seconds
    pub priority: LoadBalancingPriority,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LoadBalancingPriority {
    Immediate,
    High,
    Medium,
    Low,
    Scheduled,
}

/// Activity monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityMonitorConfig {
    pub sleep_threshold_minutes: u32,
    pub cold_threshold_hours: u32,
    pub check_interval_seconds: u32,
    pub auto_state_transitions: bool,
    pub payment_integration_enabled: bool,
}

/// User isolation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserIsolationConfig {
    pub chroot_enabled: bool,
    pub namespace_isolation: bool,
    pub cgroup_limits: CGroupLimits,
    pub security_profile: SecurityProfile,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CGroupLimits {
    pub cpu_shares: u32,
    pub memory_limit_mb: u64,
    pub memory_reservation_mb: u64,
    pub swap_limit_mb: u64,
    pub io_weight: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityProfile {
    pub allowed_capabilities: Vec<String>,
    pub dropped_capabilities: Vec<String>,
    pub read_only_root: bool,
    pub no_new_privileges: bool,
    pub seccomp_profile: Option<String>,
}

/// Request/Response models for API endpoints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserEnvironmentRequest {
    pub user_id: String,
    pub plan: String,
    pub initial_state: Option<UserState>,
    pub ssh_public_key: Option<String>,
    pub custom_quota: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChangeUserStateRequest {
    pub new_state: UserState,
    pub reason: Option<String>,
    pub force: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserEnvironmentResponse {
    pub user_id: String,
    pub state: UserState,
    pub plan: String,
    pub directory_path: String,
    pub ssh_access_enabled: bool,
    pub resource_usage: UserResourceUsage,
    pub last_activity: DateTime<Utc>,
    pub server_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerDensityResponse {
    pub servers: Vec<ServerDensityInfo>,
    pub total_capacity: u32,
    pub total_users: u32,
    pub utilization_percentage: f64,
    pub optimization_recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActivityMonitorStatus {
    pub monitor_active: bool,
    pub last_check: DateTime<Utc>,
    pub users_checked: u32,
    pub state_transitions: u32,
    pub errors: Vec<String>,
}

/// Bulk operations for efficiency
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkStateChangeRequest {
    pub user_ids: Vec<String>,
    pub new_state: UserState,
    pub reason: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkStateChangeResponse {
    pub successful: Vec<String>,
    pub failed: HashMap<String, String>, // user_id -> error_message
    pub total_processed: u32,
}

/// Migration and optimization models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserMigrationPlan {
    pub user_id: String,
    pub from_server: String,
    pub to_server: String,
    pub migration_type: MigrationType,
    pub estimated_downtime: u32, // seconds
    pub scheduled_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MigrationType {
    StateChange,     // Moving between hot/cold/sleep
    LoadBalancing,   // Moving for better distribution
    ServerMaintenance, // Moving due to server issues
    Optimization,    // Moving for better resource utilization
}

/// Error types specific to shared hosting operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SharedHostingError {
    UserNotFound(String),
    InvalidStateTransition { from: UserState, to: UserState },
    QuotaExceeded { user_id: String, limit: String },
    SSHKeyValidationFailed(String),
    ServerCapacityExceeded(String),
    PaymentRequired(String),
    MigrationFailed { user_id: String, reason: String },
}

impl std::fmt::Display for SharedHostingError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SharedHostingError::UserNotFound(id) => write!(f, "User {} not found", id),
            SharedHostingError::InvalidStateTransition { from, to } => {
                write!(f, "Invalid state transition from {:?} to {:?}", from, to)
            }
            SharedHostingError::QuotaExceeded { user_id, limit } => {
                write!(f, "Quota exceeded for user {}: {}", user_id, limit)
            }
            SharedHostingError::SSHKeyValidationFailed(user_id) => {
                write!(f, "SSH key validation failed for user {}", user_id)
            }
            SharedHostingError::ServerCapacityExceeded(server_id) => {
                write!(f, "Server {} has reached capacity", server_id)
            }
            SharedHostingError::PaymentRequired(user_id) => {
                write!(f, "Payment required for user {}", user_id)
            }
            SharedHostingError::MigrationFailed { user_id, reason } => {
                write!(f, "Migration failed for user {}: {}", user_id, reason)
            }
        }
    }
}

impl std::error::Error for SharedHostingError {}
