import { useState, useEffect, useCallback, useRef } from 'react';
import { useRequestDeduplication } from '../utils/requestDeduplicator';

interface UseApiCallOptions<T> {
  /**
   * Unique key for this API call (used for deduplication)
   */
  key: string;
  
  /**
   * Function that returns a promise with the API call
   */
  apiCall: () => Promise<T>;
  
  /**
   * Dependencies that should trigger a refetch when changed
   */
  dependencies?: any[];
  
  /**
   * Whether to automatically fetch on mount (default: true)
   */
  autoFetch?: boolean;
  
  /**
   * Initial data value
   */
  initialData?: T | null;
}

interface UseApiCallReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for making API calls with proper memoization and deduplication
 * Prevents infinite loading loops and duplicate requests
 */
export function useApiCall<T = any>({
  key,
  apiCall,
  dependencies = [],
  autoFetch = true,
  initialData = null
}: UseApiCallOptions<T>): UseApiCallReturn<T> {
  const [data, setData] = useState<T | null>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Use ref to track loading state to prevent it from being included in dependencies
  const loadingRef = useRef(false);
  // Store the API call function in a ref to prevent it from causing re-renders
  const apiCallRef = useRef(apiCall);
  const { execute, generateKey } = useRequestDeduplication();

  // Update the ref when apiCall changes
  useEffect(() => {
    apiCallRef.current = apiCall;
  }, [apiCall]);

  // Memoize the fetch function to prevent recreation on every render
  const fetchData = useCallback(async () => {
    // Prevent duplicate calls
    if (loadingRef.current) {
      console.log(`⏳ Skipping duplicate call for: ${key}`);
      return;
    }

    loadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      // Execute the API call with deduplication
      const requestKey = generateKey(key, { dependencies });
      const result = await execute(requestKey, apiCallRef.current);

      setData(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      setError(error);
      console.error(`❌ API call failed for ${key}:`, error);
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, [key, execute, generateKey]);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [key, autoFetch, ...dependencies]);

  // Manual refetch function
  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch
  };
}

/**
 * Simplified hook for basic data fetching without deduplication
 * Use this when you don't need advanced features
 */
export function useSimpleApiCall<T = any>(
  apiCall: () => Promise<T>,
  dependencies: any[] = []
): UseApiCallReturn<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadingRef = useRef(false);
  // Store the API call function in a ref to prevent it from causing re-renders
  const apiCallRef = useRef(apiCall);

  // Update the ref when apiCall changes
  useEffect(() => {
    apiCallRef.current = apiCall;
  }, [apiCall]);

  const fetchData = useCallback(async () => {
    if (loadingRef.current) {
      console.log('⏳ Skipping duplicate simple call');
      return;
    }

    loadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const result = await apiCallRef.current();
      setData(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      setError(error);
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [...dependencies]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}
