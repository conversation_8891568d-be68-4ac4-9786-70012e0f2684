cargo :    Compiling achidas v0.1.0 (D:\workspace\.rust\achidas)
At line:1 char:1
+ cargo build 2>&1 | tee build_log.txt
+ ~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (   Compiling ac...\.rust\achidas):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
warning: unused imports: `AddDatabaseReadReplicaRequest`, `CreateDatabaseConnectionPoolRequest`, 
`CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, 
`CreateStorageGatewayExportRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, 
`StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, `UpdateDatabaseConnectionPoolRequest`, 
`UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, 
`VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, 
`VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, 
`VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrStorageGatewayExportConfig`, 
`VultrVFSAttachment`, and `models::{
             }`
  --> src\controllers\vultr.rs:3:5
   |
3  | /     models::{
4  | |     },
   | |_____^
...
34 |           SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
   |                                      ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
35 |           VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
   |           ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
36 |           VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
   |           ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
37 |           CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
38 |           VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
39 |           VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
40 |           AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
41 |           RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
42 |           CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43 |           VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
55 |           CreateStorageGatewayExportRequest, VultrStorageGatewayExportConfig, VultrUser,
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
56 |           CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
57 |           UpdateVFSRequest, VultrVFSAttachment, VultrVFSRegion, BareMetalActionRequest,
   |                             ^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unused import: `EnvironmentGroup`
 --> src\controllers\environment.rs:4:49
  |
4 |     services::environment::{EnvironmentService, EnvironmentGroup},
  |                                                 ^^^^^^^^^^^^^^^^

warning: unused import: `self`
  --> src\controllers\logs.rs:13:23
   |
13 | use futures::stream::{self, Stream};
   |                       ^^^^

warning: unused import: `crate::with_circuit_breaker`
   --> src\controllers\logs.rs:305:13
    |
305 |         use crate::with_circuit_breaker;
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::with_circuit_breaker`
   --> src\controllers\logs.rs:401:13
    |
401 |         use crate::with_circuit_breaker;
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `StatusCode`
 --> src\controllers\webhooks.rs:9:23
  |
9 |     http::{HeaderMap, StatusCode},
  |                       ^^^^^^^^^^

warning: unused import: `error`
  --> src\controllers\webhooks.rs:14:15
   |
14 | use tracing::{error, info, instrument, warn};
   |               ^^^^^

warning: unused import: `serde_json::json`
  --> src\controllers\mod.rs:23:5
   |
23 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `ServiceResult`
 --> src\infrastructure\circuit_breaker.rs:2:37
  |
2 | use crate::services::{ServiceError, ServiceResult};
  |                                     ^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\infrastructure\state_machine.rs:3:27
  |
3 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused imports: `clock::DefaultClock` and `state::InMemoryState`
 --> src\infrastructure\rate_limiter.rs:1:62
  |
1 | use governor::{Quota, RateLimiter, DefaultDirectRateLimiter, clock::DefaultClock, state::InMemoryState};
  |                                                              ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\infrastructure\metrics.rs:4:21
  |
4 | use tracing::{info, error};
  |                     ^^^^^

warning: unused import: `std::collections::VecDeque`
 --> src\infrastructure\chunk_processor.rs:7:5
  |
7 | use std::collections::VecDeque;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\middleware\auth.rs:14:15
   |
14 | use tracing::{error, warn};
   |               ^^^^^

warning: unused imports: `HeaderValue` and `response::Response`
 --> src\middleware\cors.rs:2:20
  |
2 |     http::{header, HeaderValue, Method},
  |                    ^^^^^^^^^^^
3 |     response::Response,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused imports: `HeaderMap` and `extract::Query`
  --> src\middleware\mod.rs:6:46
   |
6  |     http::{Request, StatusCode, HeaderValue, HeaderMap},
   |                                              ^^^^^^^^^
...
11 |     extract::Query,
   |     ^^^^^^^^^^^^^^

warning: unused import: `validator::Validate`
 --> src\models\user.rs:3:5
  |
3 | use validator::Validate;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `validator::Validate`
 --> src\models\instance.rs:4:5
  |
4 | use validator::Validate;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src\models\regions.rs:2:5
  |
2 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Level`
  --> src\observability\mod.rs:13:21
   |
13 | use tracing::{info, Level};
   |                     ^^^^^

warning: unused imports: `Duration` and `Utc`
  --> src\services\auth.rs:12:14
   |
12 | use chrono::{Duration, Utc};
   |              ^^^^^^^^  ^^^

warning: unused imports: `DateTime` and `Utc`
  --> src\services\billing.rs:14:14
   |
14 | use chrono::{DateTime, Utc, Datelike, Timelike};
   |              ^^^^^^^^  ^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\blueprint.rs:17:5
   |
17 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `with_circuit_breaker`
  --> src\services\build.rs:15:5
   |
15 |     with_circuit_breaker,
   |     ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\build.rs:21:5
   |
21 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::process::Stdio`
  --> src\services\build.rs:23:5
   |
23 | use std::process::Stdio;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `tokio::process::Command`
  --> src\services\build.rs:24:5
   |
24 | use tokio::process::Command;
   |     ^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AsyncBufReadExt` and `BufReader`
  --> src\services\build.rs:25:17
   |
25 | use tokio::io::{AsyncBufReadExt, AsyncReadExt, BufReader};
   |                 ^^^^^^^^^^^^^^^                ^^^^^^^^^

warning: unused import: `warn`
  --> src\services\build.rs:26:40
   |
26 | use tracing::{error, info, instrument, warn};
   |                                        ^^^^

warning: unused import: `uuid::Uuid`
  --> src\services\build.rs:27:5
   |
27 | use uuid::Uuid;
   |     ^^^^^^^^^^

warning: unused imports: `AutoScalingConfig`, `BlueprintServiceType as ServiceType`, `DeploymentTrigger`, `Environment`, and 
`with_circuit_breaker`
  --> src\services\deployment.rs:10:52
   |
10 |         Application, Deployment, DeploymentStatus, DeploymentTrigger, ApplicationStatus,
   |                                                    ^^^^^^^^^^^^^^^^^
11 |         CreateApplicationRequest, ApplicationResponse, DeploymentResponse, TriggerDeploymentRequest,
12 |         Environment, EnvironmentResponse, RuntimeConfig, BlueprintServiceType as ServiceType,
   |         ^^^^^^^^^^^                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
13 |         AutoScalingConfig, PaginationQuery
   |         ^^^^^^^^^^^^^^^^^
...
17 |     with_circuit_breaker,
   |     ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\deployment.rs:23:5
   |
23 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\deployment.rs:24:15
   |
24 | use tracing::{error, info, instrument, warn};
   |               ^^^^^

warning: unused import: `DiskPagination as Pagination`
 --> src\services\disk.rs:8:46
  |
8 |     models::{Disk, DiskStatus, DiskSnapshot, DiskPagination as Pagination},
  |                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `error` and `warn`
  --> src\services\disk.rs:18:15
   |
18 | use tracing::{error, info, instrument, warn};
   |               ^^^^^                    ^^^^

warning: unused imports: `error` and `warn`
  --> src\services\domain.rs:17:15
   |
17 | use tracing::{error, info, instrument, warn};
   |               ^^^^^                    ^^^^

warning: unused import: `Environment`
 --> src\services\environment.rs:4:27
  |
4 |     models::{Application, Environment},
  |                           ^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\environment.rs:18:15
   |
18 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused imports: `GitCommit` and `GitUser`
 --> src\services\git.rs:6:53
  |
6 |         CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser
  |                                                     ^^^^^^^^^  ^^^^^^^

warning: unused import: `anyhow::Result`
  --> src\services\git.rs:10:5
   |
10 | use anyhow::Result;
   |     ^^^^^^^^^^^^^^

warning: unused import: `chrono::Utc`
  --> src\services\git.rs:12:5
   |
12 | use chrono::Utc;
   |     ^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
  --> src\services\git.rs:16:5
   |
16 | use std::collections::HashMap;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\git.rs:17:15
   |
17 | use tracing::{error, info, instrument, warn};
   |               ^^^^^

warning: unused import: `error`
  --> src\services\instance.rs:14:15
   |
14 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `CreateInstanceRequest`
 --> src\services\intelligent_hosting.rs:4:26
  |
4 |     vultr::{VultrClient, CreateInstanceRequest},
  |                          ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src\services\intelligent_hosting.rs:6:5
  |
6 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `error` and `warn`
 --> src\services\intelligent_hosting.rs:9:33
  |
9 | use tracing::{info, instrument, warn, error};
  |                                 ^^^^  ^^^^^

warning: unused imports: `Duration` and `sleep`
  --> src\services\intelligent_hosting.rs:10:19
   |
10 | use tokio::time::{sleep, Duration};
   |                   ^^^^^  ^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src\services\kubernetes_deployment.rs:6:5
  |
6 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src\services\kubernetes_deployment.rs:9:33
  |
9 | use tracing::{info, instrument, warn};
  |                                 ^^^^

warning: unused imports: `LoadBalancingDecision`, `LoadBalancingPriority`, `MigrationType`, `SharedHostingError`, 
`UserIsolationConfig`, and `UserMigrationPlan`
 --> src\services\shared_hosting.rs:4:28
  |
4 |         ServerDensityInfo, LoadBalancingDecision, LoadBalancingPriority, ActivityMonitorConfig,
  |                            ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^
5 |         UserIsolationConfig, CreateUserEnvironmentRequest, ChangeUserStateRequest,
  |         ^^^^^^^^^^^^^^^^^^^
6 |         UserEnvironmentResponse, ServerDensityResponse, BulkStateChangeRequest,
7 |         BulkStateChangeResponse, UserMigrationPlan, MigrationType, SharedHostingError
  |                                  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^

warning: unused import: `DateTime`
  --> src\services\shared_hosting.rs:13:14
   |
13 | use chrono::{DateTime, Utc};
   |              ^^^^^^^^

warning: unused import: `error`
  --> src\services\shared_hosting.rs:15:27
   |
15 | use tracing::{info, warn, error, instrument};
   |                           ^^^^^

warning: unused import: `serde_json`
  --> src\services\shared_hosting.rs:17:5
   |
17 | use serde_json;
   |     ^^^^^^^^^^

warning: unused import: `ServiceError`
 --> src\services\shared_hosting_orchestrator.rs:4:24
  |
4 |         ServiceResult, ServiceError,
  |                        ^^^^^^^^^^^^

warning: unused imports: `SharedHostingError` and `UserResourceUsage`
 --> src\services\state_management.rs:3:57
  |
3 |         UserState, UserEnvironmentState, PaymentStatus, UserResourceUsage,
  |                                                         ^^^^^^^^^^^^^^^^^
4 |         ActivityMonitorConfig, SharedHostingError
  |                                ^^^^^^^^^^^^^^^^^^

warning: unused import: `warn`
  --> src\services\state_management.rs:12:21
   |
12 | use tracing::{info, warn, error, instrument};
   |                     ^^^^

warning: unused imports: `SharedHostingError` and `UserSSHKey`
 --> src\services\ssh_key_management.rs:2:14
  |
2 |     models::{UserSSHKey, UserState, SharedHostingError},
  |              ^^^^^^^^^^             ^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src\services\ssh_key_management.rs:8:5
  |
8 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `CGroupLimits` and `UserResourceUsage`
 --> src\services\resource_allocation.rs:2:25
  |
2 |     models::{UserState, UserResourceUsage, CGroupLimits, PaymentStatus},
  |                         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^

warning: unused import: `error`
 --> src\services\resource_allocation.rs:9:27
  |
9 | use tracing::{info, warn, error, instrument};
  |                           ^^^^^

warning: unused imports: `MigrationType` and `ServerDensityInfo`
 --> src\services\load_balancing.rs:4:9
  |
4 |         ServerDensityInfo, UserMigrationPlan, MigrationType
  |         ^^^^^^^^^^^^^^^^^                     ^^^^^^^^^^^^^

warning: unused import: `Duration`
  --> src\services\load_balancing.rs:10:29
   |
10 | use chrono::{DateTime, Utc, Duration};
   |                             ^^^^^^^^

warning: unused import: `warn`
  --> src\services\load_balancing.rs:12:21
   |
12 | use tracing::{info, warn, error, instrument};
   |                     ^^^^

warning: unused import: `UserEnvironmentState`
 --> src\services\payment_access_control.rs:2:40
  |
2 |     models::{UserState, PaymentStatus, UserEnvironmentState},
  |                                        ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src\services\payment_access_control.rs:8:5
  |
8 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::controllers::ControllerError`
  --> src\services\mod.rs:25:5
   |
25 | use crate::controllers::ControllerError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `DateTime`
 --> src\utils\mod.rs:3:14
  |
3 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `Response`
 --> src\vultr\mod.rs:3:42
  |
3 | use reqwest::{header::HeaderMap, Client, Response};
  |                                          ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\vultr\mod.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
 --> src\vultr\mod.rs:7:5
  |
7 | use futures::TryStreamExt;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\vultr\models.rs:1:14
  |
1 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: use of deprecated function `base64::decode`: Use Engine::decode
   --> src\services\blueprint.rs:442:37
    |
442 |         let content_bytes = base64::decode(content_b64.replace('\n', ""))
    |                                     ^^^^^^
    |
    = note: `#[warn(deprecated)]` on by default

warning: use of deprecated function `base64::decode`: Use Engine::decode
   --> src\services\blueprint.rs:481:37
    |
481 |         let content_bytes = base64::decode(content_b64)
    |                                     ^^^^^^

warning: use of deprecated function `base64::encode`: Use Engine::encode
   --> src\services\shared_hosting.rs:457:41
    |
457 |         Ok(format!("SHA256:{}", base64::encode(result)))
    |                                         ^^^^^^

warning: unused variable: `state`
   --> src\controllers\auth.rs:113:11
    |
113 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `state`
   --> src\controllers\intelligent_hosting.rs:325:11
    |
325 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src\controllers\intelligent_hosting.rs:368:11
    |
368 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src\controllers\shared_hosting.rs:160:11
    |
160 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `claims`
   --> src\controllers\logs.rs:117:5
    |
117 |     claims: Claims,
    |     ^^^^^^ help: if this is intentional, prefix it with an underscore: `_claims`

warning: unused variable: `signature`
  --> src\controllers\webhooks.rs:23:9
   |
23 |     let signature = headers
   |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`

warning: unused variable: `token`
  --> src\controllers\webhooks.rs:68:9
   |
68 |     let token = headers
   |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_token`

warning: unused variable: `state`
   --> src\controllers\webhooks.rs:215:11
    |
215 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
  --> src\controllers\regions.rs:23:11
   |
23 |     State(state): State<Arc<AppState>>,
   |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
  --> src\controllers\regions.rs:49:11
   |
49 |     State(state): State<Arc<AppState>>,
   |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
  --> src\controllers\regions.rs:73:11
   |
73 |     State(state): State<Arc<AppState>>,
   |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src\controllers\regions.rs:106:11
    |
106 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src\controllers\regions.rs:150:11
    |
150 |     State(state): State<Arc<AppState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `chunk`
   --> src\infrastructure\rate_limiter.rs:168:13
    |
168 |         for chunk in requests.chunks(5) {
    |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_chunk`

warning: unused variable: `chunk`
   --> src\infrastructure\rate_limiter.rs:184:13
    |
184 |         for chunk in requests.chunks(10) {
    |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_chunk`

warning: unused variable: `collection`
  --> src\infrastructure\database_indexes.rs:36:13
   |
36 |         let collection = self.database.collection::<Document>("users");
   |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
  --> src\infrastructure\database_indexes.rs:81:13
   |
81 |         let collection = self.database.collection::<Document>("applications");
   |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:143:13
    |
143 |         let collection = self.database.collection::<Document>("deployments");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:205:13
    |
205 |         let collection = self.database.collection::<Document>("build_jobs");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `collection`
   --> src\infrastructure\database_indexes.rs:272:13
    |
272 |         let collection = self.database.collection::<Document>("environment_groups");
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_collection`

warning: unused variable: `index_doc`
   --> src\infrastructure\database_indexes.rs:494:36
    |
494 |                     while let Some(index_doc) = cursor.try_next().await.map_err(|e| ServiceError::Database(e))? {
    |                                    ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_index_doc`

warning: unused variable: `method`
   --> src\observability\mod.rs:138:9
    |
138 |     let method = req.method().clone();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_method`

warning: unused variable: `path`
   --> src\observability\mod.rs:139:9
    |
139 |     let path = req.uri().path().to_string();
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_path`

warning: unused variable: `duration`
   --> src\observability\mod.rs:143:9
    |
143 |     let duration = start.elapsed();
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_duration`

warning: unused variable: `status`
   --> src\observability\mod.rs:144:9
    |
144 |     let status = response.status().as_u16();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_status`

warning: unused variable: `blueprint_id`
   --> src\services\blueprint.rs:629:43
    |
629 |     async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {
    |                                           ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_blueprint_id`

warning: unused variable: `name`
   --> src\services\blueprint.rs:629:67
    |
629 |     async fn find_existing_service(&self, blueprint_id: ObjectId, name: &str) -> ServiceResult<Option<String>> {
    |                                                                   ^^^^ help: if this is intentional, prefix it with an 
underscore: `_name`

warning: unused variable: `blueprint_id`
   --> src\services\blueprint.rs:642:36
    |
642 |     async fn create_service(&self, blueprint_id: ObjectId, config: &serde_json::Value) -> ServiceResult<String> {
    |                                    ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_blueprint_id`

warning: unused variable: `metadata`
   --> src\services\build.rs:801:25
    |
801 |                     let metadata = entry.metadata().await
    |                         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_metadata`

warning: unused variable: `request`
   --> src\services\deployment.rs:239:9
    |
239 |         request: TriggerDeploymentRequest,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_request`

warning: unused variable: `update_result`
   --> src\services\deployment.rs:302:13
    |
302 |         let update_result = self.deployments
    |             ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_update_result`

warning: unused variable: `deployment_id`
   --> src\services\deployment.rs:375:9
    |
375 |         deployment_id: ObjectId,
    |         ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_deployment_id`

warning: unused variable: `build_job_id`
   --> src\services\deployment.rs:376:9
    |
376 |         build_job_id: String,
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_build_job_id`

warning: unused variable: `state_manager`
   --> src\services\deployment.rs:377:13
    |
377 |         mut state_manager: DeploymentStateManager,
    |             ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_state_manager`

warning: variable does not need to be mutable
   --> src\services\deployment.rs:377:9
    |
377 |         mut state_manager: DeploymentStateManager,
    |         ----^^^^^^^^^^^^^
    |         |
    |         help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: unused variable: `application`
   --> src\services\deployment.rs:472:46
    |
472 |     async fn determine_build_priority(&self, application: &Application) -> crate::models::BuildPriority {
    |                                              ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_application`

warning: unused variable: `deployment`
   --> src\services\deployment.rs:920:13
    |
920 |         let deployment = self.deployments
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_deployment`

warning: unused variable: `request`
   --> src\services\disk.rs:396:73
    |
396 |     async fn update_infrastructure_disk(&self, infrastructure_id: &str, request: &UpdateDiskRequest) -> 
ServiceResult<()> {
    |                                                                         ^^^^^^^ help: if this is intentional, prefix 
it with an underscore: `_request`

warning: unused variable: `webhook_secret`
   --> src\services\git.rs:288:97
    |
288 | ...sitory: &Repository, webhook_secret: &str) -> ServiceResult<String> {
    |                         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_webhook_secret`

warning: unused variable: `applications`
   --> src\services\git.rs:338:13
    |
338 |         let applications = self.applications
    |             ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_applications`

warning: variable does not need to be mutable
   --> src\services\git.rs:346:13
    |
346 |         let mut triggered_deployments = Vec::new();
    |             ----^^^^^^^^^^^^^^^^^^^^^
    |             |
    |             help: remove this `mut`

warning: unused variable: `setup_script`
   --> src\services\intelligent_hosting.rs:311:13
    |
311 |         let setup_script = match pool_type {
    |             ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_setup_script`

error[E0382]: borrow of moved value: `request.plan`
  --> src\services\shared_hosting.rs:98:52
   |
93 |             plan: request.plan,
   |                   ------------ value moved here
...
98 |             server_id: self.select_optimal_server(&request.plan.clone()).await?,
   |                                                    ^^^^^^^^^^^^ value borrowed here after move
   |
   = note: move occurs because `request.plan` has type `std::string::String`, which does not implement the `Copy` trait

warning: unused variable: `plan`
   --> src\services\shared_hosting.rs:421:43
    |
421 |     async fn select_optimal_server(&self, plan: &str) -> ServiceResult<String> {
    |                                           ^^^^ help: if this is intentional, prefix it with an underscore: `_plan`

warning: unused variable: `server_state`
   --> src\services\resource_allocation.rs:151:9
    |
151 |         server_state: &ServerResourceState,
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_server_state`

warning: unused variable: `all_servers`
   --> src\services\load_balancing.rs:213:9
    |
213 |         all_servers: &[ServerMetrics],
    |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_all_servers`

warning: unused variable: `server_id`
   --> src\services\load_balancing.rs:401:41
    |
401 |     async fn get_users_on_server(&self, server_id: &str) -> ServiceResult<Vec<MigrationCandidate>> {
    |                                         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_server_id`

warning: unused variable: `server_id`
   --> src\services\load_balancing.rs:478:46
    |
478 |     async fn get_cold_users_on_server(&self, server_id: &str) -> ServiceResult<Vec<String>> {
    |                                              ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_server_id`

warning: unused variable: `user_id`
   --> src\services\load_balancing.rs:453:55
    |
453 |     async fn create_user_environment_on_server(&self, user_id: &str, server_id: &str) -> ServiceResult<()> {
    |                                                       ^^^^^^^ help: if this is intentional, prefix it with an 
underscore: `_user_id`

warning: unused variable: `server_id`
   --> src\services\load_balancing.rs:453:70
    |
453 |     async fn create_user_environment_on_server(&self, user_id: &str, server_id: &str) -> ServiceResult<()> {
    |                                                                      ^^^^^^^^^ help: if this is intentional, prefix it 
with an underscore: `_server_id`

warning: unused variable: `user_id`
   --> src\services\load_balancing.rs:458:36
    |
458 |     async fn sync_user_data(&self, user_id: &str, target_server: &str) -> ServiceResult<()> {
    |                                    ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `target_server`
   --> src\services\load_balancing.rs:458:51
    |
458 |     async fn sync_user_data(&self, user_id: &str, target_server: &str) -> ServiceResult<()> {
    |                                                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an 
underscore: `_target_server`

warning: unused variable: `user_id`
   --> src\services\load_balancing.rs:463:41
    |
463 |     async fn update_user_routing(&self, user_id: &str, target_server: &str) -> ServiceResult<()> {
    |                                         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `target_server`
   --> src\services\load_balancing.rs:463:56
    |
463 |     async fn update_user_routing(&self, user_id: &str, target_server: &str) -> ServiceResult<()> {
    |                                                        ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an 
underscore: `_target_server`

warning: unused variable: `user_id`
   --> src\services\load_balancing.rs:468:50
    |
468 |     async fn cleanup_old_user_environment(&self, user_id: &str) -> ServiceResult<()> {
    |                                                  ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `user_id`
   --> src\services\load_balancing.rs:473:51
    |
473 |     async fn update_user_server_assignment(&self, user_id: &str, server_id: &str) -> ServiceResult<()> {
    |                                                   ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `server_id`
   --> src\services\load_balancing.rs:473:66
    |
473 |     async fn update_user_server_assignment(&self, user_id: &str, server_id: &str) -> ServiceResult<()> {
    |                                                                  ^^^^^^^^^ help: if this is intentional, prefix it 
with an underscore: `_server_id`

warning: unused variable: `threshold`
   --> src\services\state_management.rs:231:21
    |
231 |                 let threshold = Utc::now() - Duration::days(*days as i64);
    |                     ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_threshold`

warning: unused variable: `user_id`
   --> src\services\state_management.rs:355:44
    |
355 |     async fn get_user_current_state(&self, user_id: &str) -> ServiceResult<UserState> {
    |                                            ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `user_id`
   --> src\services\state_management.rs:365:51
    |
365 |     async fn update_user_state_in_database(&self, user_id: &str, state: &UserState) -> ServiceResult<()> {
    |                                                   ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `state`
   --> src\services\state_management.rs:365:66
    |
365 |     async fn update_user_state_in_database(&self, user_id: &str, state: &UserState) -> ServiceResult<()> {
    |                                                                  ^^^^^ help: if this is intentional, prefix it with an 
underscore: `_state`

warning: unused variable: `key_pair`
   --> src\services\ssh_key_management.rs:374:36
    |
374 |     async fn store_key_pair(&self, key_pair: &SSHKeyPair) -> ServiceResult<()> {
    |                                    ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_key_pair`

warning: unused variable: `user_id`
   --> src\services\ssh_key_management.rs:401:41
    |
401 |     async fn deactivate_key_pair(&self, user_id: &str) -> ServiceResult<()> {
    |                                         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `user_id`
   --> src\services\ssh_key_management.rs:428:46
    |
428 |     async fn get_user_access_attempts(&self, user_id: &str) -> ServiceResult<Vec<SSHAccessAttempt>> {
    |                                              ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `user_id`
   --> src\services\ssh_key_management.rs:438:48
    |
438 |     async fn get_recent_failed_attempts(&self, user_id: &str, source_ip: &str) -> ServiceResult<u32> {
    |                                                ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `source_ip`
   --> src\services\ssh_key_management.rs:438:63
    |
438 |     async fn get_recent_failed_attempts(&self, user_id: &str, source_ip: &str) -> ServiceResult<u32> {
    |                                                               ^^^^^^^^^ help: if this is intentional, prefix it with 
an underscore: `_source_ip`

warning: unused variable: `servers`
   --> src\services\load_balancing.rs:483:46
    |
483 |     async fn resource_aware_selection(&self, servers: &[ServerMetrics], user_state: &UserState) -> 
ServiceResult<String> {
    |                                              ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_servers`

warning: unused variable: `user_state`
   --> src\services\load_balancing.rs:483:73
    |
483 |     async fn resource_aware_selection(&self, servers: &[ServerMetrics], user_state: &UserState) -> 
ServiceResult<String> {
    |                                                                         ^^^^^^^^^^ help: if this is intentional, 
prefix it with an underscore: `_user_state`

warning: unused variable: `servers`
   --> src\services\load_balancing.rs:488:51
    |
488 |     async fn intelligent_density_selection(&self, servers: &[ServerMetrics], user_state: &UserState) -> 
ServiceResult<String> {
    |                                                   ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_servers`

warning: unused variable: `user_state`
   --> src\services\load_balancing.rs:488:78
    |
488 |     async fn intelligent_density_selection(&self, servers: &[ServerMetrics], user_state: &UserState) -> 
ServiceResult<String> {
    |                                                                              ^^^^^^^^^^ help: if this is intentional, 
prefix it with an underscore: `_user_state`

warning: unused variable: `servers`
   --> src\services\load_balancing.rs:493:49
    |
493 |     async fn least_connections_selection(&self, servers: &[ServerMetrics]) -> ServiceResult<String> {
    |                                                 ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_servers`

warning: unused variable: `thresholds`
   --> src\services\activity_monitoring.rs:118:13
    |
118 |         let thresholds = self.thresholds.clone();
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_thresholds`

warning: unused variable: `database`
   --> src\services\activity_monitoring.rs:403:37
    |
403 |     async fn process_activity_event(database: &Database, event: &ActivityEvent) -> ServiceResult<()> {
    |                                     ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_database`

warning: unused variable: `user_id`
   --> src\services\activity_monitoring.rs:440:45
    |
440 |     async fn get_user_payment_status(&self, user_id: &str) -> ServiceResult<PaymentStatus> {
    |                                             ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `user_id`
   --> src\services\activity_monitoring.rs:423:46
    |
423 |     async fn get_user_activity_events(&self, user_id: &str, duration: Duration) -> ServiceResult<Vec<ActivityEvent>> {
    |                                              ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `duration`
   --> src\services\activity_monitoring.rs:423:61
    |
423 |     async fn get_user_activity_events(&self, user_id: &str, duration: Duration) -> ServiceResult<Vec<ActivityEvent>> {
    |                                                             ^^^^^^^^ help: if this is intentional, prefix it with an 
underscore: `_duration`

warning: unused variable: `user_id`
   --> src\services\activity_monitoring.rs:445:47
    |
445 |     async fn update_user_last_activity(&self, user_id: &str) -> ServiceResult<()> {
    |                                               ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `user_id`
   --> src\services\payment_access_control.rs:449:44
    |
449 |     async fn get_user_current_state(&self, user_id: &str) -> ServiceResult<UserState> {
    |                                            ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_user_id`

warning: unused variable: `response`
   --> src\vultr\mod.rs:251:13
    |
251 |         let response = self.client.delete(&url).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_response`

error[E0599]: no variant or associated item named `Configuration` found for enum `services::ServiceError` in the current 
scope
   --> src\services\hosting_plans.rs:180:55
    |
180 |             return Err(crate::services::ServiceError::Configuration(
    |                                                       ^^^^^^^^^^^^^ variant or associated item not found in 
`ServiceError`
    |
   ::: src\services\mod.rs:29:1
    |
29  | pub enum ServiceError {
    | --------------------- variant or associated item `Configuration` not found for this enum

error[E0599]: no variant or associated item named `Hot` found for enum `PoolType` in the current scope
    --> src\services\intelligent_hosting.rs:1752:23
     |
68   | pub enum PoolType {
     | ----------------- variant or associated item `Hot` not found for this enum
...
1752 |             PoolType::Hot => plan.contains("vhf") || plan.contains("vhp"), // High-frequency/performance plans
     |                       ^^^ variant or associated item not found in `PoolType`

error[E0599]: no variant or associated item named `Cold` found for enum `PoolType` in the current scope
    --> src\services\intelligent_hosting.rs:1753:23
     |
68   | pub enum PoolType {
     | ----------------- variant or associated item `Cold` not found for this enum
...
1753 |             PoolType::Cold => plan.contains("vc2") || plan.contains("voc"), // Regular compute plans
     |                       ^^^^ variant or associated item not found in `PoolType`

error[E0599]: no variant or associated item named `Inactive` found for enum `ServerStatus` in the current scope
    --> src\services\intelligent_hosting.rs:1768:98
     |
90   | pub enum ServerStatus {
     | --------------------- variant or associated item `Inactive` not found for this enum
...
1768 |             status: if instance.status == "active" { ServerStatus::Active } else { ServerStatus::Inactive },
     |                                                                                                  ^^^^^^^^ variant or 
associated item not found in `ServerStatus`

error[E0609]: no field `monthly_cost` on type `vultr::models::VultrInstanceDetailed`
    --> src\services\intelligent_hosting.rs:1770:36
     |
1770 |             monthly_cost: instance.monthly_cost.unwrap_or(0.0),
     |                                    ^^^^^^^^^^^^ unknown field
     |
     = note: available fields are: `id`, `os`, `ram`, `disk`, `main_ip` ... and 25 others

error[E0609]: no field `cpu_cores` on type `&ResourceAllocation`
    --> src\services\intelligent_hosting.rs:1788:31
     |
1788 |         let cpu_cores = specs.cpu_cores.unwrap_or(1);
     |                               ^^^^^^^^^ unknown field
     |
help: a field with a similar name exists
     |
1788 -         let cpu_cores = specs.cpu_cores.unwrap_or(1);
1788 +         let cpu_cores = specs.cpu_shares.unwrap_or(1);
     |

error[E0308]: mismatched types
    --> src\services\intelligent_hosting.rs:1789:51
     |
1789 |         let memory_gb = specs.memory_gb.unwrap_or(1.0);
     |                                         --------- ^^^ expected `u32`, found floating-point number
     |                                         |
     |                                         arguments to this method are incorrect
     |
help: the return type of this call is `{float}` due to the type of the argument passed
    --> src\services\intelligent_hosting.rs:1789:25
     |
1789 |         let memory_gb = specs.memory_gb.unwrap_or(1.0);
     |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^---^
     |                                                   |
     |                                                   this argument influences the return type of `unwrap_or`
note: method defined here
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\option.rs:998:12
     |
998  |     pub fn unwrap_or(self, default: T) -> T {
     |            ^^^^^^^^^

error[E0609]: no field `cpu_cores` on type `&ResourceAllocation`
    --> src\services\intelligent_hosting.rs:1803:31
     |
1803 |         let cpu_cores = specs.cpu_cores.unwrap_or(2);
     |                               ^^^^^^^^^ unknown field
     |
help: a field with a similar name exists
     |
1803 -         let cpu_cores = specs.cpu_cores.unwrap_or(2);
1803 +         let cpu_cores = specs.cpu_shares.unwrap_or(2);
     |

error[E0308]: mismatched types
    --> src\services\intelligent_hosting.rs:1804:51
     |
1804 |         let memory_gb = specs.memory_gb.unwrap_or(4.0);
     |                                         --------- ^^^ expected `u32`, found floating-point number
     |                                         |
     |                                         arguments to this method are incorrect
     |
help: the return type of this call is `{float}` due to the type of the argument passed
    --> src\services\intelligent_hosting.rs:1804:25
     |
1804 |         let memory_gb = specs.memory_gb.unwrap_or(4.0);
     |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^---^
     |                                                   |
     |                                                   this argument influences the return type of `unwrap_or`
note: method defined here
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\option.rs:998:12
     |
998  |     pub fn unwrap_or(self, default: T) -> T {
     |            ^^^^^^^^^

warning: use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead
  --> src\utils\mod.rs:43:9
   |
43 |     now.timestamp_nanos().hash(&mut hasher);
   |         ^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
   --> src\controllers\logs.rs:307:13
    |
307 |         use futures::TryStreamExt;
    |             ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
   --> src\controllers\logs.rs:403:13
    |
403 |         use futures::TryStreamExt;
    |             ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\blueprint.rs:14:5
   |
14 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\build.rs:19:5
   |
19 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\deployment.rs:21:5
   |
21 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
  --> src\services\domain.rs:14:5
   |
14 | use futures::TryStreamExt;
   |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused variable: `branch`
   --> src\controllers\applications.rs:479:9
    |
479 |     let branch = simple.branch.unwrap_or_else(|| "main".to_string());
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_branch`

warning: unused variable: `limiter`
   --> src\infrastructure\rate_limiter.rs:260:13
    |
260 |         let limiter = self.limiters
    |             ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_limiter`

warning: unused variable: `handle`
   --> src\observability\mod.rs:112:9
    |
112 |     let handle = builder
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_handle`

warning: unused variable: `profile`
   --> src\services\resource_allocation.rs:414:43
    |
414 |     fn determine_allocation_reason(&self, profile: &ResourceAllocationProfile, server_state: &ServerResourceState) -> 
AllocationReason {
    |                                           ^^^^^^^ help: if this is intentional, prefix it with an underscore: 
`_profile`

warning: unused variable: `user`
   --> src\services\load_balancing.rs:439:41
    |
439 |     fn calculate_migration_score(&self, user: &MigrationCandidate, target: &ServerMetrics) -> f64 {
    |                                         ^^^^ help: if this is intentional, prefix it with an underscore: `_user`

Some errors have detailed explanations: E0308, E0382, E0599, E0609.
For more information about an error, try `rustc --explain E0308`.
warning: `achidas` (lib) generated 165 warnings
error: could not compile `achidas` (lib) due to 10 previous errors; 165 warnings emitted
