use crate::{
    controllers::{success_response, <PERSON><PERSON><PERSON><PERSON>, Controller<PERSON><PERSON><PERSON>},
    middleware::auth::get_current_user,
    models::{
        ApiResponse, CreateUserEnvironmentRequest, ChangeUserStateRequest,
        UserEnvironmentResponse, ServerDensityResponse, BulkStateChangeRequest,
        BulkStateChangeResponse, ActivityMonitorStatus, UserState
    },
    services::shared_hosting::SharedHostingService,
    AppState,
};
use axum::{
    extract::{Request, State, Path, Query},
    Json,
    body::Body,
};
use std::sync::Arc;
use tracing::instrument;
use serde::Deserialize;

#[derive(Deserialize)]
pub struct StateChangeQuery {
    pub force: Option<bool>,
}

/// Create a new user environment with isolation
#[instrument(skip(state, req))]
pub async fn create_user_environment(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    <PERSON>son(request): <PERSON><PERSON><CreateUserEnvironmentRequest>,
) -> ControllerResult<Json<ApiResponse<UserEnvironmentResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Validate request
    if request.user_id.is_empty() {
        return Err(ControllerError::Validation("user_id is required".to_string()));
    }
    
    if request.plan.is_empty() {
        return Err(ControllerError::Validation("plan is required".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .create_user_environment(request)
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to create user environment: {}", e)))?;

    Ok(success_response(response))
}

/// Get user environment information
#[instrument(skip(state, req))]
pub async fn get_user_environment(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<UserEnvironmentResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Users can only access their own environment, admins can access any
    if claims.sub != user_id && claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Access denied".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .get_user_environment(&user_id)
        .await
        .map_err(|e| ControllerError::NotFound(format!("User environment not found: {}", e)))?;

    Ok(success_response(response))
}

/// Change user state (hot/cold/sleep)
#[instrument(skip(state, req))]
pub async fn change_user_state(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Path(user_id): Path<String>,
    Query(query): Query<StateChangeQuery>,
    Json(mut request): Json<ChangeUserStateRequest>,
) -> ControllerResult<Json<ApiResponse<UserEnvironmentResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Users can only change their own state, admins can change any
    if claims.sub != user_id && claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Access denied".to_string()));
    }

    // Apply force parameter from query
    if request.force.is_none() {
        request.force = query.force;
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .change_user_state(&user_id, request)
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to change user state: {}", e)))?;

    Ok(success_response(response))
}

/// Authenticate SSH access for a user
#[instrument(skip(state))]
pub async fn authenticate_ssh_access(
    State(state): State<Arc<AppState>>,
    Path(user_id): Path<String>,
    Json(ssh_key): Json<String>,
) -> ControllerResult<Json<ApiResponse<bool>>> {
    if user_id.is_empty() {
        return Err(ControllerError::Validation("user_id is required".to_string()));
    }
    
    if ssh_key.is_empty() {
        return Err(ControllerError::Validation("ssh_key is required".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let authenticated = shared_hosting_service
        .authenticate_ssh_access(&user_id, &ssh_key)
        .await
        .map_err(|e| ControllerError::Internal(format!("SSH authentication failed: {}", e)))?;

    Ok(success_response(authenticated))
}

/// Get server density information (Admin only)
#[instrument(skip(state, req))]
pub async fn get_server_density(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
) -> ControllerResult<Json<ApiResponse<ServerDensityResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Admin only endpoint
    if claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Admin access required".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .get_server_density()
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to get server density: {}", e)))?;

    Ok(success_response(response))
}

/// Bulk state change for multiple users (Admin only)
#[instrument(skip(state, req))]
pub async fn bulk_change_state(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Json(request): Json<BulkStateChangeRequest>,
) -> ControllerResult<Json<ApiResponse<BulkStateChangeResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Admin only endpoint
    if claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Admin access required".to_string()));
    }

    // Validate request
    if request.user_ids.is_empty() {
        return Err(ControllerError::Validation("user_ids cannot be empty".to_string()));
    }
    
    if request.reason.is_empty() {
        return Err(ControllerError::Validation("reason is required".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .bulk_change_state(request)
        .await
        .map_err(|e| ControllerError::Internal(format!("Bulk state change failed: {}", e)))?;

    Ok(success_response(response))
}

/// Get activity monitor status (Admin only)
#[instrument(skip(state, req))]
pub async fn get_activity_monitor_status(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
) -> ControllerResult<Json<ApiResponse<ActivityMonitorStatus>>> {
    let claims = get_current_user(&req)?;
    
    // Admin only endpoint
    if claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Admin access required".to_string()));
    }

    // Mock activity monitor status - in production, this would query the actual monitor
    let status = ActivityMonitorStatus {
        monitor_active: true,
        last_check: chrono::Utc::now(),
        users_checked: 150,
        state_transitions: 12,
        errors: vec![],
    };

    Ok(success_response(status))
}

/// Wake up a sleeping user
#[instrument(skip(state, req))]
pub async fn wake_up_user(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<UserEnvironmentResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Users can wake up their own environment, admins can wake up any
    if claims.sub != user_id && claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Access denied".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .change_user_state(&user_id, ChangeUserStateRequest {
            new_state: UserState::Hot,
            reason: Some("Manual wake-up".to_string()),
            force: Some(false),
        })
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to wake up user: {}", e)))?;

    Ok(success_response(response))
}

/// Put user to sleep
#[instrument(skip(state, req))]
pub async fn put_user_to_sleep(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<UserEnvironmentResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Users can put their own environment to sleep, admins can do it for any user
    if claims.sub != user_id && claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Access denied".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .change_user_state(&user_id, ChangeUserStateRequest {
            new_state: UserState::Sleep,
            reason: Some("Manual sleep".to_string()),
            force: Some(false),
        })
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to put user to sleep: {}", e)))?;

    Ok(success_response(response))
}

/// Disable user access (cold state) - Admin only
#[instrument(skip(state, req))]
pub async fn disable_user_access(
    State(state): State<Arc<AppState>>,
    req: Request<Body>,
    Path(user_id): Path<String>,
) -> ControllerResult<Json<ApiResponse<UserEnvironmentResponse>>> {
    let claims = get_current_user(&req)?;
    
    // Admin only endpoint
    if claims.role.to_string() != "Admin" {
        return Err(ControllerError::Authorization("Admin access required".to_string()));
    }

    let shared_hosting_service = SharedHostingService::new(&state.database, &state.config);
    let response = shared_hosting_service
        .change_user_state(&user_id, ChangeUserStateRequest {
            new_state: UserState::Cold,
            reason: Some("Admin disabled access".to_string()),
            force: Some(true),
        })
        .await
        .map_err(|e| ControllerError::Internal(format!("Failed to disable user access: {}", e)))?;

    Ok(success_response(response))
}
