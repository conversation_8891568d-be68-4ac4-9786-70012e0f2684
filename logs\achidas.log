2025-06-24T13:06:50.229671Z  INFO ThreadId(01) achidas::observability: Tracing initialized without <PERSON><PERSON><PERSON> with file logging
2025-06-24T13:06:50.230653Z  INFO ThreadId(01) achidas: Starting Achidas hosting platform backend
2025-06-24T13:06:50.231241Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: new
2025-06-24T13:06:50.231351Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.374994Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.375695Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379067Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.379157Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379847Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.379874Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379890Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.743758Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.745666Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.745806Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.747183Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.747309Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.748053Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.756147Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.756964Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.757110Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.836892Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.837007Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.837676Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.837781Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.837831Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:56.134305Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:56.139802Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:56.140955Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:56.141180Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.132497Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.132814Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.756526Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.756938Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.757234Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.757402Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.749393Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.750889Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.910683Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.913000Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: Connected to MongoDB database: achidas
2025-06-24T13:06:58.913139Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.913209Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.913246Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.914097Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: close time.busy=244ms time.idle=8.44s
2025-06-24T13:06:58.915031Z  INFO ThreadId(01) achidas: Database connection established
2025-06-24T13:06:58.917356Z  INFO ThreadId(01) achidas: Vultr API client initialized
2025-06-24T13:06:58.935326Z  INFO ThreadId(01) achidas: Server listening on 0.0.0.0:3000
2025-06-24T13:16:51.293569Z  INFO ThreadId(01) achidas::observability: Tracing initialized without Jaeger with file logging
2025-06-24T13:16:51.294460Z  INFO ThreadId(01) achidas: Starting Achidas hosting platform backend
2025-06-24T13:16:51.295322Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: new
2025-06-24T13:16:51.295647Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.302152Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.302404Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.302466Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.302649Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.308904Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.309176Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.309766Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.731782Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.756240Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.756561Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.758322Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.758511Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.759559Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:56.770551Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:56.771549Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:56.771715Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:56.774735Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:56.774944Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:56.775057Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:57.036025Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:57.041963Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:57.045118Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:57.045601Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:58.908329Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:58.908943Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:58.909322Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:58.909511Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.771845Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:17:00.773399Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.944739Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:17:00.951694Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: Connected to MongoDB database: achidas
2025-06-24T13:17:00.952390Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.952502Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:17:00.952567Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.954399Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: close time.busy=61.3ms time.idle=9.60s
2025-06-24T13:17:00.955582Z  INFO ThreadId(01) achidas: Database connection established
2025-06-24T13:17:00.959599Z  INFO ThreadId(01) achidas: Vultr API client initialized
2025-06-24T13:17:01.006328Z  INFO ThreadId(01) achidas: Server listening on 0.0.0.0:3000
2025-06-24T13:17:17.819201Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: new
2025-06-24T13:17:17.819498Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: enter
2025-06-24T13:17:17.820539Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: exit
2025-06-24T13:17:17.820714Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: enter
2025-06-24T13:17:17.820818Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: exit
2025-06-24T13:17:17.821787Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: close time.busy=1.15ms time.idle=1.46ms
2025-06-24T13:17:17.826420Z  INFO ThreadId(08) achidas::middleware: Request completed method=GET uri=/api/v1/public/hosting/plans status=200 OK duration_ms=8
2025-06-24T13:18:13.278573Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: new
2025-06-24T13:18:13.278716Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: enter
2025-06-24T13:18:13.281394Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: exit
2025-06-24T13:18:13.403245Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: close time.busy=2.67ms time.idle=122ms
2025-06-24T13:18:13.489568Z  WARN ThreadId(07) achidas::middleware: Request failed method=POST path=/api/v1/users/applications/simple status=422 Unprocessable Entity request_id=Some("3c34413e-44b6-460d-9652-871eab8d8bd0")
2025-06-24T13:18:13.680248Z  INFO ThreadId(07) achidas::middleware: Request completed method=POST uri=/api/v1/users/applications/simple status=422 Unprocessable Entity duration_ms=212
