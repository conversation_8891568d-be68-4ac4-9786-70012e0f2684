2025-06-24T13:06:50.229671Z  INFO ThreadId(01) achidas::observability: Tracing initialized without <PERSON><PERSON><PERSON> with file logging
2025-06-24T13:06:50.230653Z  INFO ThreadId(01) achidas: Starting Achidas hosting platform backend
2025-06-24T13:06:50.231241Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: new
2025-06-24T13:06:50.231351Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.374994Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.375695Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379067Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.379157Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379847Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.379874Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379890Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.743758Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.745666Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.745806Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.747183Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.747309Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.748053Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.756147Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.756964Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.757110Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.836892Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.837007Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.837676Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.837781Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.837831Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:56.134305Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:56.139802Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:56.140955Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:56.141180Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.132497Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.132814Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.756526Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.756938Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.757234Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.757402Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.749393Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.750889Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.910683Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.913000Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: Connected to MongoDB database: achidas
2025-06-24T13:06:58.913139Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.913209Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.913246Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.914097Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: close time.busy=244ms time.idle=8.44s
2025-06-24T13:06:58.915031Z  INFO ThreadId(01) achidas: Database connection established
2025-06-24T13:06:58.917356Z  INFO ThreadId(01) achidas: Vultr API client initialized
2025-06-24T13:06:58.935326Z  INFO ThreadId(01) achidas: Server listening on 0.0.0.0:3000
