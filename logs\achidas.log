2025-06-24T13:06:50.229671Z  INFO ThreadId(01) achidas::observability: Tracing initialized without <PERSON><PERSON><PERSON> with file logging
2025-06-24T13:06:50.230653Z  INFO ThreadId(01) achidas: Starting Achidas hosting platform backend
2025-06-24T13:06:50.231241Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: new
2025-06-24T13:06:50.231351Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.374994Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.375695Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379067Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.379157Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379847Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.379874Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.379890Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.743758Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.745666Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.745806Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.747183Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:50.747309Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:50.748053Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.756147Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.756964Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.757110Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.836892Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.837007Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.837676Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:55.837781Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:55.837831Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:56.134305Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:56.139802Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:56.140955Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:56.141180Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.132497Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.132814Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.756526Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.756938Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:57.757234Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:57.757402Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.749393Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.750889Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.910683Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.913000Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: Connected to MongoDB database: achidas
2025-06-24T13:06:58.913139Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.913209Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:06:58.913246Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:06:58.914097Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: close time.busy=244ms time.idle=8.44s
2025-06-24T13:06:58.915031Z  INFO ThreadId(01) achidas: Database connection established
2025-06-24T13:06:58.917356Z  INFO ThreadId(01) achidas: Vultr API client initialized
2025-06-24T13:06:58.935326Z  INFO ThreadId(01) achidas: Server listening on 0.0.0.0:3000
2025-06-24T13:16:51.293569Z  INFO ThreadId(01) achidas::observability: Tracing initialized without Jaeger with file logging
2025-06-24T13:16:51.294460Z  INFO ThreadId(01) achidas: Starting Achidas hosting platform backend
2025-06-24T13:16:51.295322Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: new
2025-06-24T13:16:51.295647Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.302152Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.302404Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.302466Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.302649Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.308904Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.309176Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.309766Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.731782Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.756240Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.756561Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.758322Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:51.758511Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:51.759559Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:56.770551Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:56.771549Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:56.771715Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:56.774735Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:56.774944Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:56.775057Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:57.036025Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:57.041963Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:57.045118Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:57.045601Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:58.908329Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:58.908943Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:16:58.909322Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:16:58.909511Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.771845Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:17:00.773399Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.944739Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:17:00.951694Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: Connected to MongoDB database: achidas
2025-06-24T13:17:00.952390Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.952502Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T13:17:00.952567Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T13:17:00.954399Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: close time.busy=61.3ms time.idle=9.60s
2025-06-24T13:17:00.955582Z  INFO ThreadId(01) achidas: Database connection established
2025-06-24T13:17:00.959599Z  INFO ThreadId(01) achidas: Vultr API client initialized
2025-06-24T13:17:01.006328Z  INFO ThreadId(01) achidas: Server listening on 0.0.0.0:3000
2025-06-24T13:17:17.819201Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: new
2025-06-24T13:17:17.819498Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: enter
2025-06-24T13:17:17.820539Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: exit
2025-06-24T13:17:17.820714Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: enter
2025-06-24T13:17:17.820818Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: exit
2025-06-24T13:17:17.821787Z  INFO ThreadId(08) list_hosting_plans{[3mquery[0m[2m=[0mPlanQuery { tier: None, service_type: None, expected_traffic: None }}: achidas::controllers::hosting_plans: close time.busy=1.15ms time.idle=1.46ms
2025-06-24T13:17:17.826420Z  INFO ThreadId(08) achidas::middleware: Request completed method=GET uri=/api/v1/public/hosting/plans status=200 OK duration_ms=8
2025-06-24T13:18:13.278573Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: new
2025-06-24T13:18:13.278716Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: enter
2025-06-24T13:18:13.281394Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: exit
2025-06-24T13:18:13.403245Z  INFO ThreadId(07) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: close time.busy=2.67ms time.idle=122ms
2025-06-24T13:18:13.489568Z  WARN ThreadId(07) achidas::middleware: Request failed method=POST path=/api/v1/users/applications/simple status=422 Unprocessable Entity request_id=Some("3c34413e-44b6-460d-9652-871eab8d8bd0")
2025-06-24T13:18:13.680248Z  INFO ThreadId(07) achidas::middleware: Request completed method=POST uri=/api/v1/users/applications/simple status=422 Unprocessable Entity duration_ms=212
2025-06-24T14:46:13.247017Z  INFO ThreadId(01) achidas::observability: Tracing initialized without Jaeger with file logging
2025-06-24T14:46:13.259369Z  INFO ThreadId(01) achidas: Starting Achidas hosting platform backend
2025-06-24T14:46:13.260434Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: new
2025-06-24T14:46:13.260813Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:13.271628Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:13.271904Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:13.272213Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:13.272547Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:13.282329Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:13.282606Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:13.283136Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:13.939522Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:13.942319Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:13.942476Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:13.944709Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:13.944877Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:13.946040Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:18.949881Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:18.951589Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:18.951875Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:18.957046Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:18.957501Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:18.958925Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:18.959210Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:18.959515Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:19.397385Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:19.415842Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:19.422256Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:19.423490Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:22.161486Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:22.162336Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:24.393253Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:24.394432Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:25.077565Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:25.079408Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:25.080322Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:25.080884Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:27.044166Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:27.047263Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:27.353223Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:27.356343Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: Connected to MongoDB database: achidas
2025-06-24T14:46:27.356574Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:27.356699Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: enter
2025-06-24T14:46:27.356815Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: exit
2025-06-24T14:46:27.358422Z  INFO ThreadId(01) new{[3mdatabase_url[0m[2m=[0m"mongodb+srv://Poolot:<EMAIL>/AchidasHQ?retryWrites=true&w=majority&appName=DBPOOL"}: achidas::database: close time.busy=66.9ms time.idle=14.0s
2025-06-24T14:46:27.360037Z  INFO ThreadId(01) achidas: Database connection established
2025-06-24T14:46:27.365393Z  INFO ThreadId(01) achidas: Vultr API client initialized
2025-06-24T14:46:27.448524Z  INFO ThreadId(01) achidas: Server listening on 0.0.0.0:3000
2025-06-24T14:50:08.719072Z  INFO ThreadId(04) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: new
2025-06-24T14:50:08.719772Z  INFO ThreadId(04) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: enter
2025-06-24T14:50:08.724035Z  INFO ThreadId(04) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: exit
2025-06-24T14:50:08.725798Z  INFO ThreadId(04) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: close time.busy=4.26ms time.idle=2.50ms
2025-06-24T14:50:08.730752Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: new
2025-06-24T14:50:08.730981Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:08.732210Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: Creating simple application for user: 68558ea9ed1c6cc238c02ad1 with request: CreateSimpleApplicationRequest { name: "Easybot", description: Some("Web Service deployment"), deployment_mode: Some(Upload), repository_url: None, branch: Some("main"), docker_image: None, service_type: NodeJsApp, hosting_tier: Some("starter"), custom_plan: Some("starter"), environment_variables: None, build_command: Some("npm install"), start_command: Some("npm start"), region: Some("ng-lag"), vultr_plan: None }
2025-06-24T14:50:08.734386Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: Request details - name: "Easybot", deployment_mode: Some(Upload), service_type: NodeJsApp, build_command: Some("npm install"), start_command: Some("npm start")
2025-06-24T14:50:08.737361Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: Deployment mode: Upload
2025-06-24T14:50:08.739290Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: Upload mode - files will be uploaded separately
2025-06-24T14:50:08.744098Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: Upload mode - no repository needed
2025-06-24T14:50:08.746423Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: Processing simple application - build_command: Some("npm install"), start_command: "npm start", deployment_mode: Upload
2025-06-24T14:50:08.749075Z  INFO ThreadId(04) create_simple_application:create_application: achidas::services::deployment: new
2025-06-24T14:50:08.749494Z  INFO ThreadId(04) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:08.751624Z  INFO ThreadId(04) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:08.751817Z  INFO ThreadId(04) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:08.752491Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:08.752890Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:08.759628Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:08.759839Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:08.760512Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:08.760850Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:08.761314Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:08.762367Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:08.762932Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:08.767437Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:08.768299Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:08.768482Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:12.734717Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:12.734906Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:12.735329Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:12.735424Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:13.466440Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:13.466783Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:13.467918Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:13.468029Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:13.468490Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:13.468623Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:13.468787Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:13.468865Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:15.462549Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:15.462760Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:15.464437Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:15.464640Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:15.939978Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:15.940234Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:15.941480Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:15.941619Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:15.942560Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:15.942722Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:15.945515Z  INFO ThreadId(06) create_simple_application:create_application:provision_infrastructure: achidas::services::deployment: new
2025-06-24T14:50:15.945900Z  INFO ThreadId(06) create_simple_application:create_application:provision_infrastructure: achidas::services::deployment: enter
2025-06-24T14:50:15.946311Z  INFO ThreadId(06) create_simple_application:create_application:provision_infrastructure: achidas::services::deployment: exit
2025-06-24T14:50:15.946504Z  INFO ThreadId(06) create_simple_application:create_application:provision_infrastructure: achidas::services::deployment: enter
2025-06-24T14:50:15.946665Z  INFO ThreadId(06) create_simple_application:create_application:provision_infrastructure: achidas::services::deployment: exit
2025-06-24T14:50:15.948957Z  INFO ThreadId(06) create_simple_application:create_application:provision_infrastructure: achidas::services::deployment: close time.busy=573µs time.idle=2.30ms
2025-06-24T14:50:15.963003Z  INFO ThreadId(06) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:15.964201Z  INFO ThreadId(06) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:15.968351Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:15.968548Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:15.977508Z  INFO ThreadId(09) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:15.977700Z  INFO ThreadId(09) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:16.378453Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:16.378669Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:16.379974Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:16.380279Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:16.381192Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:16.381521Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:16.384547Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: Application created successfully: 685abb20ae47ead4be79abbd
2025-06-24T14:50:16.385649Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:16.385924Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: enter
2025-06-24T14:50:16.386211Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: exit
2025-06-24T14:50:16.388004Z  INFO ThreadId(08) create_simple_application:create_application: achidas::services::deployment: close time.busy=49.7ms time.idle=7.59s
2025-06-24T14:50:16.389895Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:16.390242Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: enter
2025-06-24T14:50:16.390442Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: exit
2025-06-24T14:50:16.391770Z  INFO ThreadId(08) create_simple_application: achidas::controllers::applications: close time.busy=83.6ms time.idle=7.58s
2025-06-24T14:50:16.396217Z  INFO ThreadId(08) achidas::middleware: Request completed method=POST uri=/api/v1/users/applications/simple status=200 OK duration_ms=7677
2025-06-24T14:50:19.986746Z  INFO ThreadId(08) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: new
2025-06-24T14:50:19.986898Z  INFO ThreadId(08) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: enter
2025-06-24T14:50:19.987371Z  INFO ThreadId(08) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: exit
2025-06-24T14:50:19.988538Z  INFO ThreadId(08) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: close time.busy=467µs time.idle=1.34ms
2025-06-24T14:50:19.989403Z  INFO ThreadId(08) get_application: achidas::controllers::applications: new
2025-06-24T14:50:19.989596Z  INFO ThreadId(08) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:19.990008Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: new
2025-06-24T14:50:19.990133Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:19.990681Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:19.990744Z  INFO ThreadId(08) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:19.990953Z  INFO ThreadId(08) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:19.991001Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:19.991964Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:19.992055Z  INFO ThreadId(08) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:20.438720Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:20.438852Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:20.439440Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:20.439520Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:20.439893Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:20.439961Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:20.440952Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:20.441091Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:20.441181Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:20.442380Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: close time.busy=3.16ms time.idle=449ms
2025-06-24T14:50:20.442752Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:20.442911Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:20.443021Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:20.443765Z  INFO ThreadId(09) get_application: achidas::controllers::applications: close time.busy=6.04ms time.idle=448ms
2025-06-24T14:50:20.445177Z  INFO ThreadId(09) achidas::middleware: Request completed method=GET uri=/api/v1/users/applications/685abb20ae47ead4be79abbd status=404 Not Found duration_ms=458
2025-06-24T14:50:29.623548Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: new
2025-06-24T14:50:29.623979Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: enter
2025-06-24T14:50:29.624769Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: exit
2025-06-24T14:50:29.627034Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: close time.busy=787µs time.idle=2.74ms
2025-06-24T14:50:29.628435Z  INFO ThreadId(09) get_application: achidas::controllers::applications: new
2025-06-24T14:50:29.628996Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:29.629829Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: new
2025-06-24T14:50:29.629983Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:29.630853Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:29.631005Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:29.631456Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:29.631577Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:29.633496Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:29.633716Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:29.878268Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:29.878466Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:29.879229Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:29.879364Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:29.880098Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:29.880209Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:29.880966Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:29.881157Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T14:50:29.881292Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T14:50:29.882841Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: close time.busy=4.40ms time.idle=249ms
2025-06-24T14:50:29.883391Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:29.883504Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T14:50:29.883735Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T14:50:29.885086Z  INFO ThreadId(09) get_application: achidas::controllers::applications: close time.busy=8.92ms time.idle=248ms
2025-06-24T14:50:29.887424Z  INFO ThreadId(09) achidas::middleware: Request completed method=GET uri=/api/v1/users/applications/685abb20ae47ead4be79abbd status=404 Not Found duration_ms=265
2025-06-24T14:50:34.158938Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: new
2025-06-24T14:50:34.159235Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: enter
2025-06-24T14:50:34.159743Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: exit
2025-06-24T14:50:34.160945Z  INFO ThreadId(09) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: close time.busy=508µs time.idle=1.52ms
2025-06-24T14:50:34.162571Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: new
2025-06-24T14:50:34.162755Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: enter
2025-06-24T14:50:34.163825Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: exit
2025-06-24T14:50:34.164264Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: enter
2025-06-24T14:50:34.165822Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: exit
2025-06-24T14:50:34.530752Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: enter
2025-06-24T14:50:34.531437Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: exit
2025-06-24T14:50:34.532037Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: enter
2025-06-24T14:50:34.535514Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: exit
2025-06-24T14:50:34.535690Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: enter
2025-06-24T14:50:34.535772Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: exit
2025-06-24T14:50:34.541029Z  INFO ThreadId(09) list_applications{[3mpagination[0m[2m=[0mPaginationQuery { page: None, per_page: None }}: achidas::controllers::applications: close time.busy=6.90ms time.idle=372ms
2025-06-24T14:50:34.544376Z  INFO ThreadId(09) achidas::middleware: Request completed method=GET uri=/api/v1/users/applications status=200 OK duration_ms=384
2025-06-24T15:05:19.049584Z  INFO ThreadId(06) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: new
2025-06-24T15:05:19.049794Z  INFO ThreadId(06) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: enter
2025-06-24T15:05:19.050687Z  INFO ThreadId(06) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: exit
2025-06-24T15:05:19.052474Z  INFO ThreadId(06) verify_token{[3mtoken[0m[2m=[0m"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************.9POtk8OWFwTiimG2E_gYGRwpsfCGWliEoRyJBvFrTwQ"}: achidas::services::auth: close time.busy=886µs time.idle=2.03ms
2025-06-24T15:05:19.053317Z  INFO ThreadId(06) get_application: achidas::controllers::applications: new
2025-06-24T15:05:19.053421Z  INFO ThreadId(06) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:19.054186Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: new
2025-06-24T15:05:19.054386Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:19.055038Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:19.055156Z  INFO ThreadId(06) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:19.055512Z  INFO ThreadId(06) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:19.055613Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:19.058368Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:19.058579Z  INFO ThreadId(06) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:19.060084Z  INFO ThreadId(06) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:19.060330Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:19.060898Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:19.061048Z  INFO ThreadId(06) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:23.157742Z  INFO ThreadId(06) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:23.157999Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:23.158502Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:23.158615Z  INFO ThreadId(06) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:23.438961Z  INFO ThreadId(06) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:23.439119Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:23.439628Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:23.439720Z  INFO ThreadId(06) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:23.440060Z  INFO ThreadId(06) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:23.441258Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:23.441480Z  INFO ThreadId(06) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:23.441597Z  INFO ThreadId(06) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:24.483356Z  INFO ThreadId(09) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:24.483498Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:24.484582Z  INFO ThreadId(09) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:24.484740Z  INFO ThreadId(09) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:24.682303Z  INFO ThreadId(08) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:24.682443Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:24.683173Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:24.683314Z  INFO ThreadId(08) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:24.683849Z  INFO ThreadId(08) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:24.683957Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:24.685368Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:24.685492Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: enter
2025-06-24T15:05:24.685573Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: exit
2025-06-24T15:05:24.686503Z  INFO ThreadId(08) get_application:get_application: achidas::services::deployment: close time.busy=8.55ms time.idle=5.62s
2025-06-24T15:05:24.686887Z  INFO ThreadId(08) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:24.686976Z  INFO ThreadId(08) get_application: achidas::controllers::applications: enter
2025-06-24T15:05:24.687048Z  INFO ThreadId(08) get_application: achidas::controllers::applications: exit
2025-06-24T15:05:24.687781Z  INFO ThreadId(08) get_application: achidas::controllers::applications: close time.busy=14.5ms time.idle=5.62s
2025-06-24T15:05:24.689218Z  INFO ThreadId(08) achidas::middleware: Request completed method=GET uri=/api/v1/users/applications/685abb27ae47ead4be79abbe status=200 OK duration_ms=5639
