use crate::{
    models::{
        UserState, User<PERSON><PERSON><PERSON><PERSON>, UserEnvironmentState, PaymentStatus, UserResourceUsage,
        ServerDensityInfo, LoadBalancingDecision, LoadBalancingPriority, ActivityMonitorConfig,
        UserIsolationConfig, CreateUserEnvironmentRequest, ChangeUserStateRequest,
        UserEnvironmentResponse, ServerDensityResponse, BulkStateChangeRequest,
        BulkStateChangeResponse, UserMigrationPlan, MigrationType, SharedHostingError
    },
    services::{ServiceResult, ServiceError},
    database::Database,
    config::Config,
};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use tracing::{info, warn, error, instrument};
use tokio::process::Command;
use serde_json;
use mongodb::bson;
use futures::stream::TryStreamExt;

pub struct SharedHostingService {
    database: Database,
    config: Config,
    activity_config: ActivityMonitorConfig,
}

impl SharedHostingService {
    pub fn new(database: &Database, config: &Config) -> Self {
        Self {
            database: database.clone(),
            config: config.clone(),
            activity_config: ActivityMonitorConfig {
                sleep_threshold_minutes: 30,
                cold_threshold_hours: 24,
                check_interval_seconds: 300, // 5 minutes
                auto_state_transitions: true,
                payment_integration_enabled: true,
            },
        }
    }

    /// Create a new user environment with isolation
    #[instrument(skip(self))]
    pub async fn create_user_environment(&self, request: CreateUserEnvironmentRequest) -> ServiceResult<UserEnvironmentResponse> {
        info!("Creating user environment for user: {}", request.user_id);

        // Check if user environment already exists
        if self.get_user_environment(&request.user_id).await.is_ok() {
            return Err(ServiceError::Validation(format!("User environment already exists for {}", request.user_id)));
        }

        let initial_state = request.initial_state.unwrap_or(UserState::Hot);
        let directory_path = format!("/opt/achidas/users/{}/{}", 
            self.state_to_directory(&initial_state), request.user_id);

        // Create user environment on server
        self.execute_create_environment_script(&request.user_id, &initial_state, &request.plan, request.ssh_public_key.as_deref()).await?;

        // Store SSH key if provided
        if let Some(ssh_key) = request.ssh_public_key {
            self.store_user_ssh_key(&request.user_id, &ssh_key).await?;
        }

        // Create user state record
        let user_state = UserEnvironmentState {
            user_id: request.user_id.clone(),
            current_state: initial_state.clone(),
            previous_state: None,
            plan: request.plan.clone(),
            created_at: Utc::now(),
            last_activity: Utc::now(),
            last_state_change: None,
            payment_status: PaymentStatus::Active,
            resource_usage: UserResourceUsage {
                cpu_usage_percent: 0.0,
                memory_usage_mb: 0,
                storage_usage_mb: 0,
                bandwidth_usage_mb: 0,
                request_count: 0,
                last_updated: Utc::now(),
            },
            directory_path: directory_path.clone(),
            quota_limit: request.custom_quota.unwrap_or_else(|| self.get_plan_quota(&request.plan)),
            container_id: None,
        };

        // Store in database
        self.store_user_environment_state(&user_state).await?;

        let plan_name = request.plan.clone();
        Ok(UserEnvironmentResponse {
            user_id: request.user_id,
            state: initial_state,
            plan: request.plan,
            directory_path,
            ssh_access_enabled: true,
            resource_usage: user_state.resource_usage,
            last_activity: user_state.last_activity,
            server_id: self.select_optimal_server(&plan_name).await?,
        })
    }

    /// Change user state (hot/cold/sleep)
    #[instrument(skip(self))]
    pub async fn change_user_state(&self, user_id: &str, request: ChangeUserStateRequest) -> ServiceResult<UserEnvironmentResponse> {
        info!("Changing state for user {} to {:?}", user_id, request.new_state);

        let mut user_state = self.get_user_environment_state(user_id).await?;
        
        // Validate state transition
        if !self.is_valid_state_transition(&user_state.current_state, &request.new_state) && !request.force.unwrap_or(false) {
            return Err(ServiceError::Validation(format!(
                "Invalid state transition from {:?} to {:?}", 
                user_state.current_state, request.new_state
            )));
        }

        let old_state = user_state.current_state.clone();
        user_state.previous_state = Some(old_state.clone());
        user_state.current_state = request.new_state.clone();
        user_state.last_state_change = Some(Utc::now());

        // Execute state change script
        self.execute_state_change_script(user_id, &request.new_state).await?;

        // Update database
        self.store_user_environment_state(&user_state).await?;

        info!("Successfully changed user {} state from {:?} to {:?}", user_id, old_state, request.new_state);

        Ok(UserEnvironmentResponse {
            user_id: user_id.to_string(),
            state: user_state.current_state,
            plan: user_state.plan,
            directory_path: user_state.directory_path,
            ssh_access_enabled: user_state.current_state != UserState::Cold,
            resource_usage: user_state.resource_usage,
            last_activity: user_state.last_activity,
            server_id: "shared-server-1".to_string(),
        })
    }

    /// Get user environment information
    #[instrument(skip(self))]
    pub async fn get_user_environment(&self, user_id: &str) -> ServiceResult<UserEnvironmentResponse> {
        let user_state = self.get_user_environment_state(user_id).await?;

        Ok(UserEnvironmentResponse {
            user_id: user_id.to_string(),
            state: user_state.current_state,
            plan: user_state.plan,
            directory_path: user_state.directory_path,
            ssh_access_enabled: user_state.current_state != UserState::Cold,
            resource_usage: user_state.resource_usage,
            last_activity: user_state.last_activity,
            server_id: "shared-server-1".to_string(),
        })
    }

    /// Authenticate user SSH access
    #[instrument(skip(self))]
    pub async fn authenticate_ssh_access(&self, user_id: &str, provided_key: &str) -> ServiceResult<bool> {
        let user_state = self.get_user_environment_state(user_id).await?;

        // Block cold users
        if user_state.current_state == UserState::Cold {
            warn!("SSH access denied for cold user: {}", user_id);
            return Ok(false);
        }

        // Validate SSH key
        let stored_key = self.get_user_ssh_key(user_id).await?;
        if self.validate_ssh_key(&stored_key.public_key, provided_key) {
            // Update last activity
            self.update_user_activity(user_id).await?;

            // Wake up sleeping users
            if user_state.current_state == UserState::Sleep {
                info!("Waking up sleeping user: {}", user_id);
                self.change_user_state(user_id, ChangeUserStateRequest {
                    new_state: UserState::Hot,
                    reason: Some("SSH access wake-up".to_string()),
                    force: Some(false),
                }).await?;
            }

            Ok(true)
        } else {
            warn!("SSH key validation failed for user: {}", user_id);
            Ok(false)
        }
    }

    /// Get server density information for optimization
    #[instrument(skip(self))]
    pub async fn get_server_density(&self) -> ServiceResult<ServerDensityResponse> {
        let servers = self.query_server_metrics().await?;

        let total_users = servers.iter().map(|s| s.total_users).sum();
        let total_capacity = servers.iter().map(|s| s.total_users + s.available_slots).sum::<u32>();
        let utilization = (total_users as f64 / total_capacity as f64) * 100.0;

        Ok(ServerDensityResponse {
            servers,
            total_capacity,
            total_users,
            utilization_percentage: utilization,
            optimization_recommendations: vec![
                "Consider migrating cold users to optimize hot user performance".to_string(),
                "Server utilization is optimal for current load".to_string(),
            ],
        })
    }

    /// Bulk state change for multiple users
    #[instrument(skip(self))]
    pub async fn bulk_change_state(&self, request: BulkStateChangeRequest) -> ServiceResult<BulkStateChangeResponse> {
        let mut successful = Vec::new();
        let mut failed = HashMap::new();

        for user_id in request.user_ids {
            match self.change_user_state(&user_id, ChangeUserStateRequest {
                new_state: request.new_state.clone(),
                reason: Some(request.reason.clone()),
                force: Some(false),
            }).await {
                Ok(_) => successful.push(user_id),
                Err(e) => {
                    failed.insert(user_id, e.to_string());
                }
            }
        }

        Ok(BulkStateChangeResponse {
            total_processed: successful.len() as u32 + failed.len() as u32,
            successful,
            failed,
        })
    }

    // Helper methods
    async fn execute_create_environment_script(&self, user_id: &str, state: &UserState, plan: &str, ssh_key: Option<&str>) -> ServiceResult<()> {
        let state_str = self.state_to_directory(state);
        let ssh_key_arg = ssh_key.unwrap_or("");

        let output = Command::new("/opt/achidas/scripts/create-user-environment.sh")
            .args(&[user_id, &state_str, plan, ssh_key_arg])
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Failed to execute create environment script: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("Create environment script failed: {}", stderr)));
        }

        Ok(())
    }

    async fn execute_state_change_script(&self, user_id: &str, new_state: &UserState) -> ServiceResult<()> {
        let state_str = self.state_to_directory(new_state);

        let output = Command::new("/opt/achidas/scripts/change-user-state.sh")
            .args(&[user_id, &state_str])
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Failed to execute state change script: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("State change script failed: {}", stderr)));
        }

        Ok(())
    }

    fn state_to_directory(&self, state: &UserState) -> String {
        match state {
            UserState::Hot => "hot".to_string(),
            UserState::Cold => "cold".to_string(),
            UserState::Sleep => "sleep".to_string(),
        }
    }

    fn get_plan_quota(&self, plan: &str) -> String {
        match plan {
            "starter" => "500M".to_string(),
            "nano" => "1G".to_string(),
            "micro" => "2G".to_string(),
            "small" => "5G".to_string(),
            "business" => "10G".to_string(),
            _ => "1G".to_string(),
        }
    }

    fn is_valid_state_transition(&self, from: &UserState, to: &UserState) -> bool {
        match (from, to) {
            // Any state can go to any other state (business logic allows flexibility)
            _ => true,
        }
    }

    fn validate_ssh_key(&self, stored_key: &str, provided_key: &str) -> bool {
        // Simple key comparison - in production, use proper SSH key validation
        stored_key.trim() == provided_key.trim()
    }

    // Database operations (to be implemented with actual database calls)
    async fn store_user_environment_state(&self, state: &UserEnvironmentState) -> ServiceResult<()> {
        let collection = self.database.collection::<UserEnvironmentState>("user_environment_states");

        collection
            .replace_one(
                bson::doc! { "user_id": &state.user_id },
                state,
                mongodb::options::ReplaceOptions::builder().upsert(true).build(),
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Stored user environment state for user: {}", state.user_id);
        Ok(())
    }

    async fn get_user_environment_state(&self, user_id: &str) -> ServiceResult<UserEnvironmentState> {
        let collection = self.database.collection::<UserEnvironmentState>("user_environment_states");

        match collection
            .find_one(bson::doc! { "user_id": user_id }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
        {
            Some(state) => Ok(state),
            None => {
                // Create default state for new user
                let default_state = UserEnvironmentState {
                    user_id: user_id.to_string(),
                    current_state: UserState::Cold, // Start new users in cold state
                    previous_state: None,
                    plan: "starter".to_string(),
                    created_at: Utc::now(),
                    last_activity: Utc::now(),
                    last_state_change: None,
                    payment_status: PaymentStatus::Active,
                    resource_usage: UserResourceUsage {
                        cpu_usage_percent: 0.0,
                        memory_usage_mb: 0,
                        storage_usage_mb: 0,
                        bandwidth_usage_mb: 0,
                        request_count: 0,
                        last_updated: Utc::now(),
                    },
                    directory_path: format!("/opt/achidas/users/cold/{}", user_id),
                    quota_limit: "1G".to_string(),
                    container_id: None,
                };

                // Store the default state
                self.store_user_environment_state(&default_state).await?;
                Ok(default_state)
            }
        }
    }

    async fn store_user_ssh_key(&self, user_id: &str, public_key: &str) -> ServiceResult<()> {
        let collection = self.database.collection::<UserSSHKey>("user_ssh_keys");

        let ssh_key = UserSSHKey {
            user_id: user_id.to_string(),
            public_key: public_key.to_string(),
            key_fingerprint: self.generate_key_fingerprint(public_key)?,
            created_at: Utc::now(),
            last_used: None,
            is_active: true,
        };

        collection
            .replace_one(
                bson::doc! { "user_id": user_id },
                &ssh_key,
                mongodb::options::ReplaceOptions::builder().upsert(true).build(),
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Stored SSH key for user: {}", user_id);
        Ok(())
    }

    async fn get_user_ssh_key(&self, user_id: &str) -> ServiceResult<UserSSHKey> {
        let collection = self.database.collection::<UserSSHKey>("user_ssh_keys");

        collection
            .find_one(bson::doc! { "user_id": user_id, "is_active": true }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?
            .ok_or_else(|| ServiceError::NotFound(format!("SSH key not found for user: {}", user_id)))
    }

    async fn update_user_activity(&self, user_id: &str) -> ServiceResult<()> {
        let collection = self.database.collection::<UserEnvironmentState>("user_environment_states");

        collection
            .update_one(
                bson::doc! { "user_id": user_id },
                bson::doc! {
                    "$set": {
                        "last_activity": Utc::now(),
                        "resource_usage.last_updated": Utc::now()
                    }
                },
                None,
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        info!("Updated activity for user: {}", user_id);
        Ok(())
    }

    // Production helper methods
    async fn select_optimal_server(&self, plan: &str) -> ServiceResult<String> {
        let servers = self.query_server_metrics().await?;

        // Find server with lowest utilization that can handle the plan
        let optimal_server = servers
            .into_iter()
            .filter(|s| s.available_slots > 0)
            .min_by(|a, b| a.density_score.partial_cmp(&b.density_score).unwrap_or(std::cmp::Ordering::Equal))
            .ok_or_else(|| ServiceError::Internal("No available servers found".to_string()))?;

        Ok(optimal_server.server_id)
    }

    async fn query_server_metrics(&self) -> ServiceResult<Vec<ServerDensityInfo>> {
        let collection = self.database.collection::<ServerDensityInfo>("server_metrics");

        let cursor = collection
            .find(bson::doc! {}, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let servers: Vec<ServerDensityInfo> = cursor
            .try_collect()
            .await
            .map_err(|e| ServiceError::Database(e))?;

        Ok(servers)
    }

    fn generate_key_fingerprint(&self, public_key: &str) -> ServiceResult<String> {
        use sha2::{Sha256, Digest};

        let mut hasher = Sha256::new();
        hasher.update(public_key.as_bytes());
        let result = hasher.finalize();

        Ok(format!("SHA256:{}", base64::encode(result)))
    }
}
