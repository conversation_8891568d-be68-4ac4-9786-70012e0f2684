import { useState, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useSimpleApiCall } from '../../hooks/useApiCall';
import DeveloperTools from '../../components/dev-tools/DeveloperTools';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  ServerIcon,
  GlobeAltIcon,
  ClockIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChevronDownIcon,
  CodeBracketIcon,
  CommandLineIcon,
  DocumentTextIcon,
  CogIcon,
  BellIcon,
  KeyIcon,
} from '@heroicons/react/24/outline';
import ServiceLogs from '../../components/dashboard/ServiceLogs';
import ServiceEvents from '../../components/dashboard/ServiceEvents';
import ServiceEnvironment from '../../components/dashboard/ServiceEnvironment';
import { applicationApi, type Application } from '../../services/api';

// Helper function to format deployment time
const formatDeploymentTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    return diffInHours === 0 ? 'Just now' : `${diffInHours}h`;
  }
  return `${diffInDays}d`;
};

export default function ServiceDetail() {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('dashboard');

  // Memoized API call function to fetch service details from real API
  const fetchServiceData = useCallback(async () => {
    if (!id) return null;
    return await applicationApi.get(id);
  }, [id]);

  // Use the simple hook for data fetching with proper memoization
  const { data: service, loading, error, refetch } = useSimpleApiCall(
    fetchServiceData,
    [id] // Only re-fetch when id changes
  );
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-secondary-500"></div>
        <span className="ml-3 text-gray-400">Loading service details...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h2 className="text-xl font-semibold text-white mb-2">Error Loading Service</h2>
        <p className="text-gray-400 mb-6">{error.message}</p>
        <div className="space-x-4">
          <Button variant="outline" onClick={() => refetch()}>
            Try Again
          </Button>
          <Button
            variant="glass"
            as={Link}
            to="/dashboard/services"
          >
            Back to Services
          </Button>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="text-center py-12">
        <ServerIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h2 className="text-xl font-semibold text-white mb-2">Service Not Found</h2>
        <p className="text-gray-400 mb-6">The service you're looking for doesn't exist or you don't have access to it.</p>
        <Button
          variant="glass"
          as={Link}
          to="/dashboard/services"
        >
          Back to Services
        </Button>
      </div>
    );
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Running':
      case 'deployed':
      case 'success':
        return (
          <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
            <CheckCircleIcon className="mr-1 h-3 w-3 text-green-400" />
            Running
          </span>
        );
      case 'Failed':
      case 'failed':
        return (
          <span className="inline-flex items-center rounded-full bg-red-900/30 border border-red-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-red-300">
            <XCircleIcon className="h-3 w-3 mr-1 text-red-400" />
            Failed
          </span>
        );
      case 'Creating':
      case 'Building':
      case 'Deploying':
      case 'building':
      case 'started':
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-900/30 border border-yellow-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-yellow-300">
            <ClockIcon className="h-3 w-3 mr-1 text-yellow-400" />
            {status === 'Creating' ? 'Creating' : status === 'Building' ? 'Building' : status === 'Deploying' ? 'Deploying' : 'Building'}
          </span>
        );
      case 'Suspended':
      case 'Stopped':
      case 'suspended':
      case 'stopped':
        return (
          <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
            <ClockIcon className="h-3 w-3 mr-1 text-gray-400" />
            Stopped
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center rounded-full bg-blue-900/30 border border-blue-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-blue-300">
            <ClockIcon className="h-3 w-3 mr-1 text-blue-400" />
            {status}
          </span>
        );
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center">
          <div className="mr-4">
            <ServerIcon className="h-10 w-10 text-secondary-500" />
          </div>
          <div>
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-white">{service.name}</h1>
              <div className="ml-3">{getStatusBadge(service.status)}</div>
            </div>
            <div className="flex items-center mt-1 text-sm text-gray-400">
              {service.description && (
                <>
                  <span className="flex items-center">
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    {service.description}
                  </span>
                  <span className="mx-2">•</span>
                </>
              )}
              {(service.runtime_config?.service_type || service.runtime) && (
                <>
                  <span className="flex items-center">
                    <CodeBracketIcon className="h-4 w-4 mr-1" />
                    {service.runtime_config?.service_type || service.runtime}
                  </span>
                  <span className="mx-2">•</span>
                </>
              )}
              {service.repository && (
                <a
                  href={service.repository.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center hover:text-secondary-400 transition-colors duration-200"
                >
                  {service.repository.url.replace('https://github.com/', '')}
                  <span className="ml-1 text-xs border border-gray-600 rounded px-1">{service.repository.branch}</span>
                </a>
              )}
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          {service.url && (
            <Button
              variant="outline"
              size="sm"
              as="a"
              href={service.url}
              target="_blank"
              rel="noopener noreferrer"
              icon={<GlobeAltIcon className="h-4 w-4" />}
            >
              Open
            </Button>
          )}
          <Button
            variant="glass"
            size="sm"
            icon={<ArrowPathIcon className="h-4 w-4" />}
            onClick={async () => {
              try {
                await applicationApi.deploy(service.id);
                refetch(); // Refresh the service data
              } catch (error) {
                console.error('Deploy failed:', error);
              }
            }}
          >
            Deploy
            <ChevronDownIcon className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
      
      <div className="flex border-b border-white/10 mb-6">
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'dashboard'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('dashboard')}
        >
          Dashboard
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'events'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('events')}
        >
          Events
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'logs'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('logs')}
        >
          Logs
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'environment'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('environment')}
        >
          Environment
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'config'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('config')}
        >
          Configuration
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'settings'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('settings')}
        >
          Settings
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'files'
              ? 'text-secondary-400 border-b-2 border-secondary-500'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('files')}
        >
          <div className="flex items-center space-x-1">
            <CodeBracketIcon className="h-4 w-4" />
            <span>File Manager</span>
          </div>
        </button>
      </div>
      
      {activeTab === 'dashboard' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Service Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Status</h3>
                <div className="flex items-center">
                  {getStatusBadge(service.status)}
                  <span className="ml-2 text-white">{service.status === 'Running' || service.status === 'deployed' ? 'Live' : 'Offline'}</span>
                </div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Region</h3>
                <div className="text-white">{service.runtime_config?.region || service.region || 'N/A'}</div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Plan</h3>
                <div className="text-white">{service.runtime_config?.plan || 'N/A'}</div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Memory</h3>
                <div className="text-white">{service.runtime_config?.memory_mb ? `${service.runtime_config.memory_mb} MB` : 'N/A'}</div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Instances</h3>
                <div className="text-white">{service.runtime_config?.instances || 1}</div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Last Updated</h3>
                <div className="text-white">{formatDeploymentTime(service.updated_at)} ago</div>
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">URL</h3>
                {service.url ? (
                  <a
                    href={service.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-secondary-400 hover:text-secondary-300 transition-colors duration-200 truncate block"
                  >
                    {service.url}
                  </a>
                ) : (
                  <div className="text-gray-400">Not available</div>
                )}
              </div>
              <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                <h3 className="text-sm font-medium text-gray-400 mb-1">Port</h3>
                <div className="text-white">{service.runtime_config?.networking?.port || 'N/A'}</div>
              </div>
            </div>
          </Card>
          
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-white mb-4">Latest Deployment</h2>
            <div className="p-4 rounded-lg bg-white/5 border border-white/10">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className="mr-3">
                    {getStatusBadge(service.status)}
                  </div>
                  <div>
                    <h3 className="text-white font-medium">
                      {service.id.substring(0, 8)}
                    </h3>
                    <p className="text-sm text-gray-400">
                      {new Date(service.updated_at).toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="text-sm text-gray-400">
                  Created: {new Date(service.created_at).toLocaleDateString()}
                </div>
              </div>
              <div className="mt-4 flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  icon={<DocumentTextIcon className="h-4 w-4" />}
                  onClick={() => setActiveTab('logs')}
                >
                  View Logs
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  icon={<ArrowPathIcon className="h-4 w-4" />}
                  onClick={async () => {
                    try {
                      await applicationApi.deploy(service.id);
                      refetch();
                    } catch (error) {
                      console.error('Deploy failed:', error);
                    }
                  }}
                >
                  Redeploy
                </Button>
              </div>
            </div>
          </Card>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Recent Events</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActiveTab('events')}
                >
                  View All
                </Button>
              </div>
              <div className="space-y-4">
                <div className="p-3 rounded-lg bg-white/5 border border-white/10">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="mr-3">
                        {getStatusBadge(service.status)}
                      </div>
                      <div>
                        <p className="text-sm text-white">Service {service.status}</p>
                        <p className="text-xs text-gray-400">{new Date(service.updated_at).toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-8 text-center border border-dashed border-white/20 rounded-xl">
                  <p className="text-gray-400">No recent events available.</p>
                </div>
              </div>
            </Card>
            
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-white">Quick Actions</h2>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<CommandLineIcon className="h-5 w-5" />}
                >
                  Shell
                </Button>
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<CogIcon className="h-5 w-5" />}
                >
                  Settings
                </Button>
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<KeyIcon className="h-5 w-5" />}
                >
                  Environment
                </Button>
                <Button 
                  variant="outline" 
                  fullWidth
                  className="justify-start"
                  icon={<BellIcon className="h-5 w-5" />}
                >
                  Alerts
                </Button>
              </div>
            </Card>
          </div>
        </div>
      )}
      
      {activeTab === 'events' && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-white mb-6">Service Events</h2>
          <div className="p-8 text-center border border-dashed border-white/20 rounded-xl">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-white">No events available</h3>
            <p className="mt-1 text-sm text-gray-400">Events will appear here when they occur.</p>
          </div>
        </Card>
      )}

      {activeTab === 'logs' && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-white mb-6">Service Logs</h2>
          <div className="p-8 text-center border border-dashed border-white/20 rounded-xl">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-white">No logs available</h3>
            <p className="mt-1 text-sm text-gray-400">Logs will appear here when the service is running.</p>
            <div className="mt-6">
              <Button
                variant="outline"
                onClick={async () => {
                  try {
                    const logs = await applicationApi.getLogs(service.id);
                    console.log('Logs:', logs);
                  } catch (error) {
                    console.error('Failed to fetch logs:', error);
                  }
                }}
              >
                Refresh Logs
              </Button>
            </div>
          </div>
        </Card>
      )}

      {activeTab === 'environment' && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-white mb-6">Environment Variables</h2>
          {service.environment?.variables && Object.keys(service.environment.variables).length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                {Object.entries(service.environment.variables).map(([key, value]) => (
                  <div key={key} className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-white">{key}</h3>
                        <p className="text-sm text-gray-400 mt-1 font-mono">
                          {value === '***' ? '••••••••' : value}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <Button variant="glass" icon={<KeyIcon className="h-4 w-4" />}>
                  Add Variable
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center border border-dashed border-white/20 rounded-xl">
              <KeyIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-white">No environment variables</h3>
              <p className="mt-1 text-sm text-gray-400">Environment variables will be displayed here.</p>
              <div className="mt-6">
                <Button variant="glass" icon={<KeyIcon className="h-4 w-4" />}>
                  Add Variable
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}

      {activeTab === 'config' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-white mb-6">Runtime Configuration</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-sm font-medium text-gray-400 mb-2">Service Type</h3>
                  <p className="text-white">{service.runtime_config?.service_type || 'N/A'}</p>
                </div>
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-sm font-medium text-gray-400 mb-2">Instance Type</h3>
                  <p className="text-white">{service.runtime_config?.instance_type || 'N/A'}</p>
                </div>
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-sm font-medium text-gray-400 mb-2">Build Command</h3>
                  <p className="text-white font-mono text-sm">{service.environment?.build_command || 'N/A'}</p>
                </div>
                <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                  <h3 className="text-sm font-medium text-gray-400 mb-2">Start Command</h3>
                  <p className="text-white font-mono text-sm">{service.environment?.start_command || 'N/A'}</p>
                </div>
              </div>
              <div className="space-y-4">
                {service.runtime_config?.auto_scaling && (
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h3 className="text-sm font-medium text-gray-400 mb-2">Auto Scaling</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Enabled:</span>
                        <span className="text-white">{service.runtime_config.auto_scaling.enabled ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Min Instances:</span>
                        <span className="text-white">{service.runtime_config.auto_scaling.min_instances}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Max Instances:</span>
                        <span className="text-white">{service.runtime_config.auto_scaling.max_instances}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">CPU Target:</span>
                        <span className="text-white">{service.runtime_config.auto_scaling.target_cpu_percent}%</span>
                      </div>
                    </div>
                  </div>
                )}
                {service.runtime_config?.health_check && (
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h3 className="text-sm font-medium text-gray-400 mb-2">Health Check</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Enabled:</span>
                        <span className="text-white">{service.runtime_config.health_check.enabled ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Path:</span>
                        <span className="text-white font-mono">{service.runtime_config.health_check.path}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Port:</span>
                        <span className="text-white">{service.runtime_config.health_check.port}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Interval:</span>
                        <span className="text-white">{service.runtime_config.health_check.interval}s</span>
                      </div>
                    </div>
                  </div>
                )}
                {service.runtime_config?.networking && (
                  <div className="p-4 rounded-lg bg-white/5 border border-white/10">
                    <h3 className="text-sm font-medium text-gray-400 mb-2">Networking</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Port:</span>
                        <span className="text-white">{service.runtime_config.networking.port}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">SSL Enabled:</span>
                        <span className="text-white">{service.runtime_config.networking.ssl_enabled ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Custom Domains:</span>
                        <span className="text-white">{service.runtime_config.networking.custom_domains?.length || 0}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'settings' && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-white mb-6">Service Settings</h2>
          <p className="text-gray-400">Settings page content will go here.</p>
        </Card>
      )}

      {activeTab === 'files' && (
        <div className="h-[800px]">
          <DeveloperTools />
        </div>
      )}
    </div>
  );
}
