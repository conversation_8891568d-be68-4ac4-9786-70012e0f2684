{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/admin/adminlayout.tsx", "../../src/components/dashboard/activityfeed.tsx", "../../src/components/dashboard/dashboardlayout.tsx", "../../src/components/dashboard/domainlist.tsx", "../../src/components/dashboard/hostingstats.tsx", "../../src/components/dashboard/notificationsmenu.tsx", "../../src/components/dashboard/serviceenvironment.tsx", "../../src/components/dashboard/serviceevents.tsx", "../../src/components/dashboard/servicelogs.tsx", "../../src/components/dashboard/sidebar.tsx", "../../src/components/dashboard/usermenu.tsx", "../../src/components/layout/footer.tsx", "../../src/components/layout/layout.tsx", "../../src/components/layout/navbar.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/loadingbar.tsx", "../../src/components/ui/loadingscreen.tsx", "../../src/components/ui/pricingtable.tsx", "../../src/hooks/useapicall.ts", "../../src/pages/dashboard.tsx", "../../src/pages/home.tsx", "../../src/pages/login.tsx", "../../src/pages/signup.tsx", "../../src/pages/admin/billing.tsx", "../../src/pages/admin/dashboard.tsx", "../../src/pages/admin/servers.tsx", "../../src/pages/admin/settings.tsx", "../../src/pages/admin/users.tsx", "../../src/pages/dashboard/analytics.tsx", "../../src/pages/dashboard/billing.tsx", "../../src/pages/dashboard/dashboard.tsx", "../../src/pages/dashboard/domains.tsx", "../../src/pages/dashboard/hosting.tsx", "../../src/pages/dashboard/profile.tsx", "../../src/pages/dashboard/projectdetail.tsx", "../../src/pages/dashboard/servicedetail.tsx", "../../src/pages/dashboard/services.tsx", "../../src/pages/dashboard/settings.tsx", "../../src/pages/dashboard/project/projectconnections.tsx", "../../src/pages/dashboard/project/projectdashboard.tsx", "../../src/pages/dashboard/project/projectdisks.tsx", "../../src/pages/dashboard/project/projectenvironment.tsx", "../../src/pages/dashboard/project/projectevents.tsx", "../../src/pages/dashboard/project/projectjobs.tsx", "../../src/pages/dashboard/project/projectlogs.tsx", "../../src/pages/dashboard/project/projectmanage.tsx", "../../src/pages/dashboard/project/projectmetrics.tsx", "../../src/pages/dashboard/project/projectmonitor.tsx", "../../src/pages/dashboard/project/projectpreviews.tsx", "../../src/pages/dashboard/project/projectscaling.tsx", "../../src/pages/dashboard/project/projectsettings.tsx", "../../src/pages/dashboard/project/projectshell.tsx", "../../src/services/api.ts", "../../src/services/authservice.ts", "../../src/stores/authstore.ts", "../../src/utils/requestdeduplicator.ts"], "errors": true, "version": "5.8.3"}