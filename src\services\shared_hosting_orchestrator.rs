use crate::{
    models::{UserState, PaymentStatus, UserEnvironmentResponse, ServerDensityResponse},
    services::{
        ServiceResult, ServiceError,
        shared_hosting::SharedHostingService,
        state_management::StateManagementService,
        ssh_key_management::SSHKeyManagementService,
        resource_allocation::ResourceAllocationEngine,
        load_balancing::IntelligentLoadBalancer,
        activity_monitoring::ActivityMonitoringService,
        payment_access_control::PaymentAccessControlService,
    },
    database::Database,
    config::Config,
};
use chrono::{DateTime, Utc};
use std::sync::Arc;
use tracing::{info, warn, error, instrument};
use tokio::{time, sync::RwLock};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrchestrationMetrics {
    pub total_users: u32,
    pub hot_users: u32,
    pub cold_users: u32,
    pub sleep_users: u32,
    pub server_utilization: f64,
    pub active_migrations: u32,
    pub state_transitions_last_hour: u32,
    pub payment_issues: u32,
    pub system_health_score: f64,
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationRecommendations {
    pub server_optimizations: Vec<String>,
    pub user_migrations: Vec<String>,
    pub resource_adjustments: Vec<String>,
    pub cost_savings: Vec<String>,
    pub performance_improvements: Vec<String>,
}

pub struct SharedHostingOrchestrator {
    shared_hosting: SharedHostingService,
    state_management: StateManagementService,
    ssh_key_management: SSHKeyManagementService,
    resource_allocation: ResourceAllocationEngine,
    load_balancer: IntelligentLoadBalancer,
    activity_monitor: Arc<RwLock<ActivityMonitoringService>>,
    payment_control: PaymentAccessControlService,
    is_running: bool,
}

impl SharedHostingOrchestrator {
    pub fn new(database: &Database, config: &Config) -> Self {
        Self {
            shared_hosting: SharedHostingService::new(database, config),
            state_management: StateManagementService::new(database, config),
            ssh_key_management: SSHKeyManagementService::new(database, config),
            resource_allocation: ResourceAllocationEngine::new(database, config),
            load_balancer: IntelligentLoadBalancer::new(database, config),
            activity_monitor: Arc::new(RwLock::new(ActivityMonitoringService::new(database, config))),
            payment_control: PaymentAccessControlService::new(database, config),
            is_running: false,
        }
    }

    /// Start the complete shared hosting orchestration system
    #[instrument(skip(self))]
    pub async fn start_orchestration(&mut self) -> ServiceResult<()> {
        if self.is_running {
            return Ok(());
        }

        info!("🚀 Starting Achidas Shared Hosting Orchestration System");

        // Start activity monitoring
        {
            let mut monitor = self.activity_monitor.write().await;
            monitor.start_monitoring().await?;
        }

        // Start periodic orchestration cycles
        self.start_orchestration_cycles().await?;

        self.is_running = true;
        info!("✅ Shared hosting orchestration system started successfully");
        Ok(())
    }

    /// Start all periodic orchestration cycles
    async fn start_orchestration_cycles(&self) -> ServiceResult<()> {
        // Resource allocation cycle (every 5 minutes)
        let resource_engine = self.resource_allocation.clone();
        tokio::spawn(async move {
            let mut interval = time::interval(time::Duration::from_secs(300));
            loop {
                interval.tick().await;
                if let Err(e) = resource_engine.run_allocation_cycle().await {
                    error!("Resource allocation cycle failed: {}", e);
                }
            }
        });

        // Load balancing cycle (every 15 minutes)
        let load_balancer = self.load_balancer.clone();
        tokio::spawn(async move {
            let mut interval = time::interval(time::Duration::from_secs(900));
            loop {
                interval.tick().await;
                if let Err(e) = load_balancer.run_load_balancing_cycle().await {
                    error!("Load balancing cycle failed: {}", e);
                }
            }
        });

        // State management cycle (every 10 minutes)
        let state_service = self.state_management.clone();
        tokio::spawn(async move {
            let mut interval = time::interval(time::Duration::from_secs(600));
            loop {
                interval.tick().await;
                if let Err(e) = state_service.run_activity_monitor().await {
                    error!("State management cycle failed: {}", e);
                }
            }
        });

        // Payment status check (every hour)
        let payment_service = self.payment_control.clone();
        tokio::spawn(async move {
            let mut interval = time::interval(time::Duration::from_secs(3600));
            loop {
                interval.tick().await;
                if let Err(e) = payment_service.run_payment_status_check().await {
                    error!("Payment status check failed: {}", e);
                }
            }
        });

        // SSH key rotation (daily)
        let ssh_service = self.ssh_key_management.clone();
        tokio::spawn(async move {
            let mut interval = time::interval(time::Duration::from_secs(86400));
            loop {
                interval.tick().await;
                if let Err(e) = ssh_service.run_automatic_key_rotation().await {
                    error!("SSH key rotation failed: {}", e);
                }
            }
        });

        Ok(())
    }

    /// Get comprehensive system metrics
    #[instrument(skip(self))]
    pub async fn get_orchestration_metrics(&self) -> ServiceResult<OrchestrationMetrics> {
        let server_density = self.shared_hosting.get_server_density().await?;
        let monitoring_stats = {
            let monitor = self.activity_monitor.read().await;
            monitor.get_monitoring_statistics().await?
        };

        let total_users = server_density.total_users;
        let server_utilization = server_density.utilization_percentage;

        // Calculate system health score
        let health_score = self.calculate_system_health_score(&server_density, &monitoring_stats).await?;

        Ok(OrchestrationMetrics {
            total_users,
            hot_users: monitoring_stats.active_users,
            cold_users: monitoring_stats.cold_users,
            sleep_users: monitoring_stats.sleeping_users,
            server_utilization,
            active_migrations: 0, // TODO: Get from load balancer
            state_transitions_last_hour: 0, // TODO: Get from state management
            payment_issues: 0, // TODO: Get from payment control
            system_health_score: health_score,
            last_updated: Utc::now(),
        })
    }

    /// Get optimization recommendations
    #[instrument(skip(self))]
    pub async fn get_optimization_recommendations(&self) -> ServiceResult<OptimizationRecommendations> {
        let server_density = self.shared_hosting.get_server_density().await?;
        let state_optimization = self.state_management.optimize_user_distribution().await?;

        let mut recommendations = OptimizationRecommendations {
            server_optimizations: server_density.optimization_recommendations,
            user_migrations: state_optimization,
            resource_adjustments: vec![],
            cost_savings: vec![],
            performance_improvements: vec![],
        };

        // Add resource optimization recommendations
        recommendations.resource_adjustments.push(
            "Consider implementing burst capacity for paying users during low-usage periods".to_string()
        );

        // Add cost savings recommendations
        recommendations.cost_savings.push(
            format!("Current density optimization could save ~{}% on infrastructure costs", 
                   self.calculate_potential_savings(&server_density))
        );

        // Add performance improvements
        recommendations.performance_improvements.push(
            "Implement predictive scaling based on user activity patterns".to_string()
        );

        Ok(recommendations)
    }

    /// Comprehensive user onboarding
    #[instrument(skip(self))]
    pub async fn onboard_new_user(
        &self,
        user_id: &str,
        plan: &str,
        ssh_public_key: Option<String>,
        is_trial: bool,
    ) -> ServiceResult<UserEnvironmentResponse> {
        info!("🎯 Onboarding new user: {} (plan: {}, trial: {})", user_id, plan, is_trial);

        // 1. Create payment profile
        if is_trial {
            self.payment_control.create_trial_user(user_id, plan).await?;
        }

        // 2. Generate SSH key pair if provided
        if let Some(ssh_key) = &ssh_public_key {
            self.ssh_key_management.generate_user_key_pair(user_id, ssh_key).await?;
        }

        // 3. Select optimal server
        let user_state = if is_trial { UserState::Hot } else { UserState::Hot };
        let payment_status = if is_trial { PaymentStatus::Trial } else { PaymentStatus::Active };
        let optimal_server = self.load_balancer.select_optimal_server(&user_state, &payment_status).await?;

        // 4. Create user environment
        let create_request = crate::models::CreateUserEnvironmentRequest {
            user_id: user_id.to_string(),
            plan: plan.to_string(),
            initial_state: Some(user_state),
            ssh_public_key,
            custom_quota: None,
        };

        let environment = self.shared_hosting.create_user_environment(create_request).await?;

        // 5. Initialize activity monitoring
        {
            let monitor = self.activity_monitor.read().await;
            monitor.record_activity(
                user_id,
                crate::services::activity_monitoring::ActivityEventType::UserLogin,
                None,
            ).await?;
        }

        info!("✅ User {} onboarded successfully on server {}", user_id, optimal_server);
        Ok(environment)
    }

    /// Handle user state transition with full orchestration
    #[instrument(skip(self))]
    pub async fn orchestrated_state_transition(
        &self,
        user_id: &str,
        new_state: UserState,
        reason: Option<String>,
    ) -> ServiceResult<UserEnvironmentResponse> {
        info!("🔄 Orchestrating state transition for user {} to {:?}", user_id, new_state);

        // 1. Execute state transition
        let transition_result = self.state_management.execute_state_transition(
            user_id,
            new_state.clone(),
            crate::services::state_management::TransitionTrigger::ManualAdmin,
            reason,
        ).await?;

        // 2. Update shared hosting environment
        let change_request = crate::models::ChangeUserStateRequest {
            new_state: new_state.clone(),
            reason: Some(transition_result.reason),
            force: Some(false),
        };

        let environment = self.shared_hosting.change_user_state(user_id, change_request).await?;

        // 3. Trigger resource reallocation if needed
        if new_state == UserState::Hot {
            // User becoming active - may need resource adjustment
            if let Err(e) = self.resource_allocation.run_allocation_cycle().await {
                warn!("Failed to trigger resource reallocation: {}", e);
            }
        }

        // 4. Update load balancing if needed
        if new_state == UserState::Cold {
            // User going cold - may trigger load balancing optimization
            if let Err(e) = self.load_balancer.run_load_balancing_cycle().await {
                warn!("Failed to trigger load balancing: {}", e);
            }
        }

        info!("✅ State transition orchestrated successfully for user {}", user_id);
        Ok(environment)
    }

    /// Emergency system optimization
    #[instrument(skip(self))]
    pub async fn emergency_optimization(&self) -> ServiceResult<String> {
        warn!("🚨 Running emergency system optimization");

        let mut actions_taken = Vec::new();

        // 1. Force resource reallocation
        match self.resource_allocation.run_allocation_cycle().await {
            Ok(decisions) => {
                actions_taken.push(format!("Resource reallocation: {} decisions made", decisions.len()));
            },
            Err(e) => {
                error!("Emergency resource allocation failed: {}", e);
                actions_taken.push("Resource allocation failed".to_string());
            }
        }

        // 2. Force load balancing
        match self.load_balancer.run_load_balancing_cycle().await {
            Ok(decisions) => {
                actions_taken.push(format!("Load balancing: {} decisions made", decisions.len()));
            },
            Err(e) => {
                error!("Emergency load balancing failed: {}", e);
                actions_taken.push("Load balancing failed".to_string());
            }
        }

        // 3. Force state management review
        match self.state_management.run_activity_monitor().await {
            Ok(transitions) => {
                actions_taken.push(format!("State management: {} transitions made", transitions.len()));
            },
            Err(e) => {
                error!("Emergency state management failed: {}", e);
                actions_taken.push("State management failed".to_string());
            }
        }

        let summary = actions_taken.join("; ");
        info!("🔧 Emergency optimization completed: {}", summary);
        Ok(summary)
    }

    // Helper methods
    async fn calculate_system_health_score(
        &self,
        server_density: &ServerDensityResponse,
        monitoring_stats: &crate::services::activity_monitoring::MonitoringStatistics,
    ) -> ServiceResult<f64> {
        let mut score = 100.0;

        // Penalize high utilization
        if server_density.utilization_percentage > 90.0 {
            score -= 20.0;
        } else if server_density.utilization_percentage > 80.0 {
            score -= 10.0;
        }

        // Penalize low activity
        let activity_ratio = monitoring_stats.active_users as f64 / monitoring_stats.total_users_monitored as f64;
        if activity_ratio < 0.3 {
            score -= 15.0;
        }

        // Bonus for good distribution
        if server_density.utilization_percentage > 60.0 && server_density.utilization_percentage < 80.0 {
            score += 10.0;
        }

        Ok(score.max(0.0).min(100.0))
    }

    fn calculate_potential_savings(&self, server_density: &ServerDensityResponse) -> u32 {
        // Simple calculation based on utilization efficiency
        let efficiency = server_density.utilization_percentage / 100.0;
        let potential_savings = (1.0 - efficiency) * 30.0; // Up to 30% savings
        potential_savings as u32
    }
}

/// Extension trait for cloning services (needed for tokio::spawn)
trait CloneableService {
    fn clone(&self) -> Self;
}

impl CloneableService for ResourceAllocationEngine {
    fn clone(&self) -> Self {
        // This would need proper implementation based on the actual struct
        ResourceAllocationEngine::new(&self.database, &self.config)
    }
}

impl CloneableService for IntelligentLoadBalancer {
    fn clone(&self) -> Self {
        IntelligentLoadBalancer::new(&self.database, &self.config)
    }
}

impl CloneableService for StateManagementService {
    fn clone(&self) -> Self {
        StateManagementService::new(&self.database, &self.config)
    }
}

impl CloneableService for PaymentAccessControlService {
    fn clone(&self) -> Self {
        PaymentAccessControlService::new(&self.database, &self.config)
    }
}

impl CloneableService for SSHKeyManagementService {
    fn clone(&self) -> Self {
        SSHKeyManagementService::new(&self.database, &self.config)
    }
}
