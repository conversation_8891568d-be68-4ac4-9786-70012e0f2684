use crate::{
    config::Config,
    database::Database,
    models::{
        Application, GitProvider, GitWebhookPayload, Repository,
        CreateRepositoryRequest, GitRepositoryInfo, GitCommit, GitUser
    },
    services::{ServiceError, ServiceResult},
};
use anyhow::Result;
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use mongodb::Collection;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{error, info, instrument, warn};
use hmac::{Hmac, Mac};
use sha2::Sha256;
use hex;

type HmacSha256 = Hmac<Sha256>;

pub struct GitService<'a> {
    applications: Collection<Application>,
    client: Client,
    config: &'a Config,
}

impl<'a> GitService<'a> {
    pub fn new(database: &Database, config: &'a Config) -> Self {
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            applications: database.collection("applications"),
            client,
            config,
        }
    }

    #[instrument(skip(self, repository_request))]
    pub async fn validate_repository(&self, repository_request: &CreateRepositoryRequest) -> ServiceResult<GitRepositoryInfo> {
        match repository_request.provider {
            GitProvider::GitHub => self.validate_github_repository(repository_request).await,
            GitProvider::GitLab => self.validate_gitlab_repository(repository_request).await,
            GitProvider::Bitbucket => self.validate_bitbucket_repository(repository_request).await,
        }
    }

    #[instrument(skip(self, repository_request))]
    async fn validate_github_repository(&self, repository_request: &CreateRepositoryRequest) -> ServiceResult<GitRepositoryInfo> {
        let repo_path = self.extract_repo_path(&repository_request.url)?;
        let url = format!("https://api.github.com/repos/{}", repo_path);

        let mut request = self.client.get(&url);
        
        if let Some(token) = &repository_request.access_token {
            request = request.header("Authorization", format!("token {}", token));
        }

        let response = request
            .send()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("GitHub API error: {}", e)))?;

        if !response.status().is_success() {
            return Err(ServiceError::ExternalApi(format!(
                "GitHub repository not accessible: {}",
                response.status()
            )));
        }

        let repo_info: GitHubRepository = response
            .json()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse GitHub response: {}", e)))?;

        // Validate branch exists
        self.validate_github_branch(&repo_path, &repository_request.branch, repository_request.access_token.as_ref()).await?;

        Ok(GitRepositoryInfo {
            id: repo_info.id,
            name: repo_info.name,
            full_name: repo_info.full_name,
            clone_url: repo_info.clone_url,
            default_branch: repo_info.default_branch,
        })
    }

    #[instrument(skip(self, access_token))]
    async fn validate_github_branch(&self, repo_path: &str, branch: &str, access_token: Option<&String>) -> ServiceResult<()> {
        let url = format!("https://api.github.com/repos/{}/branches/{}", repo_path, branch);
        
        let mut request = self.client.get(&url);
        
        if let Some(token) = access_token {
            request = request.header("Authorization", format!("token {}", token));
        }

        let response = request
            .send()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("GitHub API error: {}", e)))?;

        if !response.status().is_success() {
            return Err(ServiceError::Validation(format!(
                "Branch '{}' not found in repository",
                branch
            )));
        }

        Ok(())
    }

    #[instrument(skip(self, repository_request))]
    async fn validate_gitlab_repository(&self, repository_request: &CreateRepositoryRequest) -> ServiceResult<GitRepositoryInfo> {
        // Extract project ID or path from GitLab URL
        let project_path = self.extract_gitlab_project_path(&repository_request.url)?;
        let encoded_path = urlencoding::encode(&project_path);
        let url = format!("https://gitlab.com/api/v4/projects/{}", encoded_path);

        let mut request = self.client.get(&url);
        
        if let Some(token) = &repository_request.access_token {
            request = request.header("PRIVATE-TOKEN", token);
        }

        let response = request
            .send()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("GitLab API error: {}", e)))?;

        if !response.status().is_success() {
            return Err(ServiceError::ExternalApi(format!(
                "GitLab repository not accessible: {}",
                response.status()
            )));
        }

        let repo_info: GitLabProject = response
            .json()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse GitLab response: {}", e)))?;

        Ok(GitRepositoryInfo {
            id: repo_info.id,
            name: repo_info.name,
            full_name: repo_info.path_with_namespace,
            clone_url: repo_info.http_url_to_repo,
            default_branch: repo_info.default_branch,
        })
    }

    #[instrument(skip(self, repository_request))]
    async fn validate_bitbucket_repository(&self, repository_request: &CreateRepositoryRequest) -> ServiceResult<GitRepositoryInfo> {
        let repo_path = self.extract_repo_path(&repository_request.url)?;
        let url = format!("https://api.bitbucket.org/2.0/repositories/{}", repo_path);

        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Bitbucket API error: {}", e)))?;

        if !response.status().is_success() {
            return Err(ServiceError::ExternalApi(format!(
                "Bitbucket repository not accessible: {}",
                response.status()
            )));
        }

        let repo_info: BitbucketRepository = response
            .json()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse Bitbucket response: {}", e)))?;

        Ok(GitRepositoryInfo {
            id: 0, // Bitbucket uses UUIDs, but we'll use 0 for now
            name: repo_info.name,
            full_name: repo_info.full_name,
            clone_url: repo_info.links.clone.iter()
                .find(|link| link.name == "https")
                .map(|link| link.href.clone())
                .unwrap_or_default(),
            default_branch: repo_info.mainbranch.name,
        })
    }

    #[instrument(skip(self, application_id, webhook_secret))]
    pub async fn setup_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
        match repository.provider {
            GitProvider::GitHub => self.setup_github_webhook(application_id, repository, webhook_secret).await,
            GitProvider::GitLab => self.********************(application_id, repository, webhook_secret).await,
            GitProvider::Bitbucket => self.setup_bitbucket_webhook(application_id, repository, webhook_secret).await,
        }
    }

    #[instrument(skip(self, application_id, repository, webhook_secret))]
    async fn setup_github_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
        let repo_path = self.extract_repo_path(&repository.url)?;
        let webhook_url = format!("{}/api/v1/webhooks/git/{}", self.config.server_address, application_id);
        
        let webhook_config = GitHubWebhookConfig {
            name: "web".to_string(),
            active: true,
            events: vec!["push".to_string(), "pull_request".to_string()],
            config: GitHubWebhookConfigDetails {
                url: webhook_url,
                content_type: "json".to_string(),
                secret: Some(webhook_secret.to_string()),
                insecure_ssl: "0".to_string(),
            },
        };

        let url = format!("https://api.github.com/repos/{}/hooks", repo_path);
        
        let mut request = self.client.post(&url);
        
        if let Some(token) = &repository.access_token {
            request = request.header("Authorization", format!("token {}", token));
        }

        let response = request
            .json(&webhook_config)
            .send()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("GitHub webhook setup error: {}", e)))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(ServiceError::ExternalApi(format!(
                "Failed to setup GitHub webhook: {}",
                error_text
            )));
        }

        let webhook_response: GitHubWebhookResponse = response
            .json()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse webhook response: {}", e)))?;

        Ok(webhook_response.id.to_string())
    }

    #[instrument(skip(self, application_id, repository, webhook_secret))]
    async fn ********************(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
        let (owner, repo) = self.parse_gitlab_url(&repository.url)?;
        let webhook_url = format!("{}/api/v1/webhooks/gitlab/{}", self.config.base_url, application_id);

        let webhook_payload = serde_json::json!({
            "url": webhook_url,
            "push_events": true,
            "merge_requests_events": true,
            "tag_push_events": true,
            "token": webhook_secret,
            "enable_ssl_verification": true
        });

        let client = reqwest::Client::new();
        let response = client
            .post(&format!("https://gitlab.com/api/v4/projects/{}%2F{}/hooks", owner, repo))
            .header("Authorization", format!("Bearer {}", repository.access_token.as_ref().unwrap_or(&String::new())))
            .header("Content-Type", "application/json")
            .json(&webhook_payload)
            .send()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("GitLab API error: {}", e)))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(ServiceError::ExternalApi(format!("GitLab webhook creation failed: {}", error_text)));
        }

        let webhook_data: serde_json::Value = response.json().await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse GitLab response: {}", e)))?;

        let webhook_id = webhook_data["id"].as_u64()
            .ok_or_else(|| ServiceError::ExternalApi("No webhook ID in GitLab response".to_string()))?;

        info!("GitLab webhook created successfully: {}", webhook_id);
        Ok(webhook_id.to_string())
    }

    #[instrument(skip(self, application_id, repository, webhook_secret))]
    async fn setup_bitbucket_webhook(&self, application_id: &ObjectId, repository: &Repository, webhook_secret: &str) -> ServiceResult<String> {
        let (owner, repo) = self.parse_bitbucket_url(&repository.url)?;
        let webhook_url = format!("{}/api/v1/webhooks/bitbucket/{}", self.config.base_url, application_id);

        let webhook_payload = serde_json::json!({
            "description": "Achidas deployment webhook",
            "url": webhook_url,
            "active": true,
            "events": ["repo:push", "pullrequest:created", "pullrequest:updated"]
        });

        let client = reqwest::Client::new();
        let response = client
            .post(&format!("https://api.bitbucket.org/2.0/repositories/{}/{}/hooks", owner, repo))
            .header("Authorization", format!("Bearer {}", repository.access_token.as_ref().unwrap_or(&String::new())))
            .header("Content-Type", "application/json")
            .json(&webhook_payload)
            .send()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Bitbucket API error: {}", e)))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(ServiceError::ExternalApi(format!("Bitbucket webhook creation failed: {}", error_text)));
        }

        let webhook_data: serde_json::Value = response.json().await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to parse Bitbucket response: {}", e)))?;

        let webhook_id = webhook_data["uuid"].as_str()
            .ok_or_else(|| ServiceError::ExternalApi("No webhook UUID in Bitbucket response".to_string()))?;

        info!("Bitbucket webhook created successfully: {}", webhook_id);
        Ok(webhook_id.to_string())
    }

    #[instrument(skip(self, payload, signature, secret))]
    pub fn verify_webhook_signature(&self, payload: &[u8], signature: &str, secret: &str) -> ServiceResult<bool> {
        let mut mac = HmacSha256::new_from_slice(secret.as_bytes())
            .map_err(|e| ServiceError::Internal(format!("Invalid webhook secret: {}", e)))?;
        
        mac.update(payload);
        let expected_signature = format!("sha256={}", hex::encode(mac.finalize().into_bytes()));
        
        Ok(signature == expected_signature)
    }

    #[instrument(skip(self, payload))]
    pub async fn process_webhook(&self, payload: GitWebhookPayload) -> ServiceResult<Vec<ObjectId>> {
        // Find applications that match this repository
        let applications = self.applications
            .find(doc! {
                "repository.url": &payload.repository.clone_url,
                "repository.auto_deploy": true
            }, None)
            .await
            .map_err(|e| ServiceError::Database(e))?;

        let mut triggered_deployments = Vec::new();

        // Process each matching application
        // This would trigger new deployments
        // Implementation would go here...

        Ok(triggered_deployments)
    }

    fn extract_repo_path(&self, url: &str) -> ServiceResult<String> {
        // Extract owner/repo from GitHub/GitLab URL
        let url = url.trim_end_matches(".git");
        let parts: Vec<&str> = url.split('/').collect();
        
        if parts.len() < 2 {
            return Err(ServiceError::Validation("Invalid repository URL".to_string()));
        }

        let owner = parts[parts.len() - 2];
        let repo = parts[parts.len() - 1];
        
        Ok(format!("{}/{}", owner, repo))
    }

    fn extract_gitlab_project_path(&self, url: &str) -> ServiceResult<String> {
        // Extract project path from GitLab URL
        let url = url.trim_end_matches(".git");
        if let Some(path) = url.strip_prefix("https://gitlab.com/") {
            Ok(path.to_string())
        } else {
            Err(ServiceError::Validation("Invalid GitLab URL".to_string()))
        }
    }
}

// GitHub API models
#[derive(Debug, Deserialize)]
struct GitHubRepository {
    id: u64,
    name: String,
    full_name: String,
    clone_url: String,
    default_branch: String,
}

#[derive(Debug, Serialize)]
struct GitHubWebhookConfig {
    name: String,
    active: bool,
    events: Vec<String>,
    config: GitHubWebhookConfigDetails,
}

#[derive(Debug, Serialize)]
struct GitHubWebhookConfigDetails {
    url: String,
    content_type: String,
    secret: Option<String>,
    insecure_ssl: String,
}

#[derive(Debug, Deserialize)]
struct GitHubWebhookResponse {
    id: u64,
}

// GitLab API models
#[derive(Debug, Deserialize)]
struct GitLabProject {
    id: u64,
    name: String,
    path_with_namespace: String,
    http_url_to_repo: String,
    default_branch: String,
}

// Bitbucket API models
#[derive(Debug, Deserialize)]
struct BitbucketRepository {
    name: String,
    full_name: String,
    links: BitbucketLinks,
    mainbranch: BitbucketBranch,
}

#[derive(Debug, Deserialize)]
struct BitbucketLinks {
    clone: Vec<BitbucketCloneLink>,
}

#[derive(Debug, Deserialize)]
struct BitbucketCloneLink {
    name: String,
    href: String,
}

#[derive(Debug, Deserialize)]
struct BitbucketBranch {
    name: String,
}
