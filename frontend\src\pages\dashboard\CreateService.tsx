import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  GlobeAltIcon,
  ServerIcon,
  CircleStackIcon,
  CodeBracketIcon,
  DocumentTextIcon,
  CloudIcon,
  ArrowLeftIcon,
  FolderIcon,
  LinkIcon,
  CubeIcon,
} from '@heroicons/react/24/outline';
import { applicationApi, hostingPlansApi, type CreateSimpleApplicationRequest, type HostingPlan } from '../../services/api';
import { useApiCall } from '../../hooks/useApiCall';

type ServiceType = 'web-service' | 'static-site' | 'database' | 'background-worker' | 'cron-job';
type SourceType = 'git' | 'upload' | 'existing-image';

const serviceTypes = [
  {
    id: 'web-service' as ServiceType,
    name: 'Web Service',
    description: 'Deploy web applications, APIs, and backend services',
    icon: GlobeAltIcon,
    features: ['Auto-scaling', 'Load balancing', 'SSL certificates', 'Custom domains'],
    examples: ['Node.js API', 'Python Flask', 'Rust Actix', 'Go Gin'],
  },
  {
    id: 'static-site' as ServiceType,
    name: 'Static Site',
    description: 'Deploy static websites and single-page applications',
    icon: DocumentTextIcon,
    features: ['CDN distribution', 'Instant deploys', 'Branch previews', 'Form handling'],
    examples: ['React SPA', 'Vue.js app', 'HTML/CSS site', 'Jekyll blog'],
  },
  {
    id: 'database' as ServiceType,
    name: 'Database',
    description: 'Managed database instances with automatic backups',
    icon: CircleStackIcon,
    features: ['Automatic backups', 'High availability', 'Monitoring', 'Scaling'],
    examples: ['PostgreSQL', 'MySQL', 'MongoDB', 'Redis'],
  },
  {
    id: 'background-worker' as ServiceType,
    name: 'Background Worker',
    description: 'Long-running background processes and job queues',
    icon: ServerIcon,
    features: ['Auto-restart', 'Resource monitoring', 'Log aggregation', 'Health checks'],
    examples: ['Queue processor', 'Data pipeline', 'Email service', 'File processor'],
  },
  {
    id: 'cron-job' as ServiceType,
    name: 'Cron Job',
    description: 'Scheduled tasks and periodic job execution',
    icon: CloudIcon,
    features: ['Flexible scheduling', 'Retry logic', 'Notifications', 'Timeout handling'],
    examples: ['Daily reports', 'Data sync', 'Cleanup tasks', 'Health checks'],
  },
];

const sourceOptions = [
  {
    id: 'git' as SourceType,
    name: 'Git Repository',
    description: 'Deploy from GitHub, GitLab, or Bitbucket',
    icon: CodeBracketIcon,
    popular: true,
  },
  {
    id: 'upload' as SourceType,
    name: 'Upload Files',
    description: 'Upload your code directly via file manager',
    icon: FolderIcon,
    popular: false,
  },
  {
    id: 'existing-image' as SourceType,
    name: 'Docker Image',
    description: 'Deploy from existing Docker image',
    icon: CubeIcon,
    popular: false,
  },
];

export default function CreateService() {
  const navigate = useNavigate();
  const [selectedServiceType, setSelectedServiceType] = useState<ServiceType | null>(null);
  const [selectedSourceType, setSelectedSourceType] = useState<SourceType | null>(null);
  const [step, setStep] = useState(1);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Fetch hosting plans from API
  const { data: hostingPlans, loading: plansLoading, error: plansError } = useApiCall({
    key: 'hosting-plans',
    apiCall: hostingPlansApi.list,
    dependencies: [],
    autoFetch: true,
  });

  // Form data for service creation
  const [formData, setFormData] = useState({
    name: '',
    repositoryUrl: '',
    branch: 'main',
    hostingTier: 'starter',
  });

  const handleServiceTypeSelect = (type: ServiceType) => {
    setSelectedServiceType(type);
    setIsTransitioning(true);
    // Auto-advance to next step after a short delay for visual feedback
    setTimeout(() => {
      setStep(2);
      setIsTransitioning(false);
    }, 800);
  };

  const handleSourceTypeSelect = (type: SourceType) => {
    setSelectedSourceType(type);
    setIsTransitioning(true);
    // Auto-advance to next step after a short delay for visual feedback
    setTimeout(() => {
      setStep(3);
      setIsTransitioning(false);
    }, 800);
  };

  const handleBack = () => {
    if (step === 1) {
      navigate('/dashboard/services');
    } else {
      setStep(step - 1);
      if (step === 2) {
        setSelectedServiceType(null);
      } else if (step === 3) {
        setSelectedSourceType(null);
      }
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-12">
      <div className="flex items-center space-x-6">
        {/* Step 1 */}
        <div className="flex flex-col items-center space-y-2">
          <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
            step >= 1
              ? 'bg-gradient-to-r from-secondary-500 to-purple-600 border-secondary-400 text-white shadow-lg shadow-secondary-500/25'
              : 'bg-gray-800 border-gray-600 text-gray-400'
          }`}>
            {step > 1 ? (
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            ) : (
              <span className="font-bold">1</span>
            )}
          </div>
          <span className={`text-xs font-medium ${step >= 1 ? 'text-white' : 'text-gray-500'}`}>
            Service Type
          </span>
        </div>

        {/* Connector */}
        <div className={`w-16 h-0.5 transition-all duration-300 ${
          step >= 2 ? 'bg-gradient-to-r from-secondary-500 to-purple-600' : 'bg-gray-700'
        }`} />

        {/* Step 2 */}
        <div className="flex flex-col items-center space-y-2">
          <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
            step >= 2
              ? 'bg-gradient-to-r from-secondary-500 to-purple-600 border-secondary-400 text-white shadow-lg shadow-secondary-500/25'
              : 'bg-gray-800 border-gray-600 text-gray-400'
          }`}>
            {step > 2 ? (
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            ) : (
              <span className="font-bold">2</span>
            )}
          </div>
          <span className={`text-xs font-medium ${step >= 2 ? 'text-white' : 'text-gray-500'}`}>
            Source
          </span>
        </div>

        {/* Connector */}
        <div className={`w-16 h-0.5 transition-all duration-300 ${
          step >= 3 ? 'bg-gradient-to-r from-secondary-500 to-purple-600' : 'bg-gray-700'
        }`} />

        {/* Step 3 */}
        <div className="flex flex-col items-center space-y-2">
          <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
            step >= 3
              ? 'bg-gradient-to-r from-secondary-500 to-purple-600 border-secondary-400 text-white shadow-lg shadow-secondary-500/25'
              : 'bg-gray-800 border-gray-600 text-gray-400'
          }`}>
            <span className="font-bold">3</span>
          </div>
          <span className={`text-xs font-medium ${step >= 3 ? 'text-white' : 'text-gray-500'}`}>
            Configure
          </span>
        </div>
      </div>
    </div>
  );

  const renderServiceTypeSelection = () => (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-3">What would you like to deploy?</h2>
        <p className="text-lg text-gray-400">Choose the type of service you want to create</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {serviceTypes.map((service) => {
          const Icon = service.icon;
          return (
            <div
              key={service.id}
              className={`group relative overflow-hidden rounded-2xl p-8 cursor-pointer transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2 ${
                selectedServiceType === service.id
                  ? 'bg-gradient-to-br from-secondary-500/20 via-secondary-600/10 to-purple-600/20 border-2 border-secondary-400 shadow-2xl shadow-secondary-500/25'
                  : 'bg-gradient-to-br from-gray-800/50 via-gray-900/50 to-black/50 border border-white/10 hover:border-secondary-500/50 hover:shadow-xl hover:shadow-secondary-500/10'
              } backdrop-blur-xl`}
              onClick={() => handleServiceTypeSelect(service.id)}
            >
              {/* Animated background gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-secondary-500/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              {/* Glow effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-secondary-500/20 to-purple-600/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />

              <div className="relative z-10 flex flex-col items-center text-center space-y-6">
                {/* Icon with animated background */}
                <div className={`relative p-4 rounded-2xl transition-all duration-300 ${
                  selectedServiceType === service.id
                    ? 'bg-gradient-to-br from-secondary-500/30 to-purple-600/30 shadow-lg'
                    : 'bg-gradient-to-br from-secondary-500/20 to-purple-600/20 group-hover:shadow-lg group-hover:shadow-secondary-500/25'
                }`}>
                  <Icon className={`h-10 w-10 transition-all duration-300 ${
                    selectedServiceType === service.id
                      ? 'text-secondary-300'
                      : 'text-secondary-400 group-hover:text-secondary-300'
                  }`} />

                  {/* Pulse animation for selected */}
                  {selectedServiceType === service.id && (
                    <div className="absolute inset-0 rounded-2xl bg-secondary-500/20 animate-pulse" />
                  )}
                </div>

                {/* Title and description */}
                <div className="space-y-3">
                  <h3 className={`text-xl font-bold transition-colors duration-300 ${
                    selectedServiceType === service.id
                      ? 'text-white'
                      : 'text-gray-100 group-hover:text-white'
                  }`}>
                    {service.name}
                  </h3>
                  <p className="text-sm text-gray-400 leading-relaxed max-w-xs">
                    {service.description}
                  </p>
                </div>

                {/* Features */}
                <div className="space-y-3 w-full">
                  <h4 className="text-xs font-semibold text-secondary-300 uppercase tracking-wider">
                    Features
                  </h4>
                  <ul className="text-xs text-gray-300 space-y-2">
                    {service.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-secondary-400" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Examples */}
                <div className="space-y-3 w-full">
                  <h4 className="text-xs font-semibold text-secondary-300 uppercase tracking-wider">
                    Examples
                  </h4>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {service.examples.slice(0, 2).map((example, index) => (
                      <span
                        key={index}
                        className="inline-block px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-gray-700/80 to-gray-800/80 text-gray-200 rounded-full border border-gray-600/50 backdrop-blur-sm"
                      >
                        {example}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Selection indicator */}
                {selectedServiceType === service.id && (
                  <div className="absolute top-4 right-4">
                    <div className="w-6 h-6 rounded-full bg-secondary-500 flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Transition indicator */}
      {selectedServiceType && isTransitioning && (
        <div className="flex justify-center pt-8">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 border-2 border-secondary-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="text-sm text-gray-400">
                Proceeding to source selection...
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Manual continue option */}
      {selectedServiceType && !isTransitioning && (
        <div className="flex justify-center pt-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setIsTransitioning(false);
              setStep(2);
            }}
            className="px-6 py-2 text-sm"
          >
            Continue Now
          </Button>
        </div>
      )}
    </div>
  );

  const renderSourceTypeSelection = () => (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-3">How do you want to deploy?</h2>
        <p className="text-lg text-gray-400">Choose your deployment source</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
        {sourceOptions.map((source) => {
          const Icon = source.icon;
          return (
            <div
              key={source.id}
              className={`group relative overflow-hidden rounded-2xl p-8 cursor-pointer transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2 ${
                selectedSourceType === source.id
                  ? 'bg-gradient-to-br from-secondary-500/20 via-secondary-600/10 to-purple-600/20 border-2 border-secondary-400 shadow-2xl shadow-secondary-500/25'
                  : 'bg-gradient-to-br from-gray-800/50 via-gray-900/50 to-black/50 border border-white/10 hover:border-secondary-500/50 hover:shadow-xl hover:shadow-secondary-500/10'
              } backdrop-blur-xl`}
              onClick={() => handleSourceTypeSelect(source.id)}
            >
              {/* Popular badge */}
              {source.popular && (
                <div className="absolute -top-3 -right-3 z-20">
                  <div className="bg-gradient-to-r from-secondary-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg animate-pulse">
                    ⭐ Popular
                  </div>
                </div>
              )}

              {/* Animated background gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-secondary-500/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              {/* Glow effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-secondary-500/20 to-purple-600/20 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500" />

              <div className="relative z-10 flex flex-col items-center text-center space-y-6">
                {/* Icon with animated background */}
                <div className={`relative p-5 rounded-2xl transition-all duration-300 ${
                  selectedSourceType === source.id
                    ? 'bg-gradient-to-br from-secondary-500/30 to-purple-600/30 shadow-lg'
                    : 'bg-gradient-to-br from-secondary-500/20 to-purple-600/20 group-hover:shadow-lg group-hover:shadow-secondary-500/25'
                }`}>
                  <Icon className={`h-12 w-12 transition-all duration-300 ${
                    selectedSourceType === source.id
                      ? 'text-secondary-300'
                      : 'text-secondary-400 group-hover:text-secondary-300'
                  }`} />

                  {/* Pulse animation for selected */}
                  {selectedSourceType === source.id && (
                    <div className="absolute inset-0 rounded-2xl bg-secondary-500/20 animate-pulse" />
                  )}
                </div>

                {/* Title and description */}
                <div className="space-y-3">
                  <h3 className={`text-xl font-bold transition-colors duration-300 ${
                    selectedSourceType === source.id
                      ? 'text-white'
                      : 'text-gray-100 group-hover:text-white'
                  }`}>
                    {source.name}
                  </h3>
                  <p className="text-sm text-gray-400 leading-relaxed max-w-xs">
                    {source.description}
                  </p>
                </div>

                {/* Selection indicator */}
                {selectedSourceType === source.id && (
                  <div className="absolute top-4 right-4">
                    <div className="w-6 h-6 rounded-full bg-secondary-500 flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Transition indicator */}
      {selectedSourceType && isTransitioning && (
        <div className="flex justify-center pt-8">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 border-2 border-secondary-500 border-t-transparent rounded-full animate-spin"></div>
              <p className="text-sm text-gray-400">
                Proceeding to configuration...
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Manual continue option */}
      {selectedSourceType && !isTransitioning && (
        <div className="flex justify-center pt-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setIsTransitioning(false);
              setStep(3);
            }}
            className="px-6 py-2 text-sm"
          >
            Continue Now
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBack}
            icon={<ArrowLeftIcon className="h-4 w-4" />}
          >
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-white">Create New Service</h1>
            <p className="text-sm text-gray-400">Deploy your application to PoolotHost</p>
          </div>
        </div>
      </div>

      {renderStepIndicator()}

      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-gray-900/80 via-gray-800/50 to-black/80 backdrop-blur-xl border border-white/10 shadow-2xl">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-secondary-500/5 via-transparent to-purple-600/5" />
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-secondary-500 via-purple-600 to-secondary-500" />

        <div className="relative z-10 p-12">
          {step === 1 && renderServiceTypeSelection()}
          {step === 2 && renderSourceTypeSelection()}
          {step === 3 && (
            <div className="space-y-8">
              <div className="text-center">
                <h2 className="text-3xl font-bold text-white mb-4">Configure Your Service</h2>
                <div className="inline-flex items-center space-x-2 px-6 py-3 rounded-full bg-gradient-to-r from-secondary-500/20 to-purple-600/20 border border-secondary-500/30">
                  <span className="text-secondary-300 font-medium">
                    {serviceTypes.find(s => s.id === selectedServiceType)?.name}
                  </span>
                  <span className="text-gray-400">via</span>
                  <span className="text-purple-300 font-medium">
                    {sourceOptions.find(s => s.id === selectedSourceType)?.name}
                  </span>
                </div>
              </div>

              {/* Configuration Form */}
              <div className="max-w-2xl mx-auto space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-white mb-2">
                    Service Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="block w-full rounded-xl border-0 py-3 px-4 text-white ring-1 ring-inset ring-white/10
                      placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                      sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                    placeholder="my-awesome-app"
                  />
                </div>

                {selectedSourceType === 'git' && (
                  <>
                    <div>
                      <label htmlFor="repositoryUrl" className="block text-sm font-medium text-white mb-2">
                        Repository URL
                      </label>
                      <input
                        type="url"
                        id="repositoryUrl"
                        value={formData.repositoryUrl}
                        onChange={(e) => setFormData({ ...formData, repositoryUrl: e.target.value })}
                        className="block w-full rounded-xl border-0 py-3 px-4 text-white ring-1 ring-inset ring-white/10
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        placeholder="https://github.com/username/repo"
                      />
                    </div>

                    <div>
                      <label htmlFor="branch" className="block text-sm font-medium text-white mb-2">
                        Branch
                      </label>
                      <input
                        type="text"
                        id="branch"
                        value={formData.branch}
                        onChange={(e) => setFormData({ ...formData, branch: e.target.value })}
                        className="block w-full rounded-xl border-0 py-3 px-4 text-white ring-1 ring-inset ring-white/10
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        placeholder="main"
                      />
                    </div>
                  </>
                )}

                <div>
                  <label htmlFor="hostingTier" className="block text-sm font-medium text-white mb-2">
                    Hosting Tier
                  </label>
                  <select
                    id="hostingTier"
                    value={formData.hostingTier}
                    onChange={(e) => setFormData({ ...formData, hostingTier: e.target.value })}
                    className="block w-full rounded-xl border-0 py-3 px-4 text-white ring-1 ring-inset ring-white/10
                      focus:ring-2 focus:ring-inset focus:ring-secondary-500
                      sm:text-sm sm:leading-6 bg-gray-900/80 backdrop-blur-sm transition-all duration-300
                      [&>option]:bg-gray-900 [&>option]:text-white [&>optgroup]:bg-gray-800 [&>optgroup]:text-gray-300"
                  >
                    {plansLoading ? (
                      <option value="">Loading plans...</option>
                    ) : plansError ? (
                      <option value="">Error loading plans</option>
                    ) : hostingPlans && hostingPlans.length > 0 ? (
                      // Group plans by tier and sort them
                      (() => {
                        const groupedPlans = hostingPlans.reduce((acc: Record<string, HostingPlan[]>, plan: HostingPlan) => {
                          if (!acc[plan.tier]) acc[plan.tier] = [];
                          acc[plan.tier].push(plan);
                          return acc;
                        }, {});

                        // Sort tiers: Shared first, then Dedicated, then Enterprise
                        const tierOrder = ['Shared', 'Dedicated', 'Enterprise'];

                        return tierOrder.map(tier => {
                          if (!groupedPlans[tier]) return null;

                          // Sort plans within each tier by price
                          const sortedPlans = groupedPlans[tier].sort((a, b) => a.price_per_month - b.price_per_month);

                          return (
                            <optgroup key={tier} label={`${tier} Hosting`}>
                              {sortedPlans.map((plan: HostingPlan) => (
                                <option key={plan.plan_name} value={plan.plan_name}>
                                  {plan.plan_name.split('-').map(word =>
                                    word.charAt(0).toUpperCase() + word.slice(1)
                                  ).join(' ')} - ${plan.price_per_month.toFixed(2)}/month
                                  {plan.max_concurrent_users && ` (${plan.max_concurrent_users} users)`}
                                </option>
                              ))}
                            </optgroup>
                          );
                        }).filter(Boolean);
                      })()
                    ) : (
                      <option value="starter">Starter ($0.50/month)</option>
                    )}
                  </select>
                </div>

                {/* Plan Details Section */}
                {hostingPlans && formData.hostingTier && (
                  (() => {
                    const selectedPlan = hostingPlans.find((plan: HostingPlan) => plan.plan_name === formData.hostingTier);
                    if (!selectedPlan) return null;

                    return (
                      <div className="mt-6 p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                        <h4 className="text-lg font-semibold text-white mb-3">
                          {selectedPlan.plan_name.split('-').map(word =>
                            word.charAt(0).toUpperCase() + word.slice(1)
                          ).join(' ')} Plan Details
                        </h4>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="text-gray-300 text-sm mb-2">{selectedPlan.description}</p>

                            <div className="space-y-1">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-400">Tier:</span>
                                <span className="text-white font-medium">{selectedPlan.tier}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-400">Price:</span>
                                <span className="text-white font-medium">
                                  ${selectedPlan.price_per_month.toFixed(2)}/month (${selectedPlan.price_per_hour.toFixed(4)}/hour)
                                </span>
                              </div>
                              {selectedPlan.max_concurrent_users && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-400">Max Users:</span>
                                  <span className="text-white font-medium">{selectedPlan.max_concurrent_users}</span>
                                </div>
                              )}
                              {selectedPlan.bandwidth_limit && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-400">Bandwidth:</span>
                                  <span className="text-white font-medium">{selectedPlan.bandwidth_limit}GB/month</span>
                                </div>
                              )}
                              {selectedPlan.vultr_plan && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-400">Server Plan:</span>
                                  <span className="text-white font-medium">{selectedPlan.vultr_plan}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          <div>
                            <h5 className="text-white font-medium mb-2">Features:</h5>
                            <ul className="space-y-1">
                              {selectedPlan.features.slice(0, 4).map((feature, index) => (
                                <li key={index} className="text-gray-300 text-sm flex items-center">
                                  <span className="w-1.5 h-1.5 bg-secondary-500 rounded-full mr-2"></span>
                                  {feature}
                                </li>
                              ))}
                              {selectedPlan.features.length > 4 && (
                                <li className="text-gray-400 text-xs">
                                  +{selectedPlan.features.length - 4} more features
                                </li>
                              )}
                            </ul>

                            <div className="mt-3">
                              <h5 className="text-white font-medium mb-1">Recommended for:</h5>
                              <div className="flex flex-wrap gap-1">
                                {selectedPlan.recommended_for.slice(0, 3).map((use, index) => (
                                  <span key={index} className="px-2 py-1 bg-secondary-500/20 text-secondary-300 text-xs rounded-md">
                                    {use}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })()
                )}

                <div className="flex justify-center pt-6">
                  <Button
                    variant="glass"
                    glow={true}
                    size="lg"
                    disabled={!formData.name || (selectedSourceType === 'git' && !formData.repositoryUrl) || isCreating}
                    onClick={async () => {
                      setIsCreating(true);
                      try {
                        const serviceTypeMap: Record<ServiceType, string> = {
                          'web-service': 'NodeJsApp',
                          'static-site': 'StaticWebsite',
                          'database': 'DockerApp',
                          'background-worker': 'NodeJsApp',
                          'cron-job': 'NodeJsApp',
                        };

                        // Create data based on source type
                        const createData: CreateSimpleApplicationRequest = {
                          name: formData.name,
                          description: `${serviceTypes.find(s => s.id === selectedServiceType)?.name} deployment`,
                          service_type: serviceTypeMap[selectedServiceType!],
                          hosting_tier: formData.hostingTier,
                          custom_plan: formData.hostingTier, // Use the selected plan name
                          region: 'ng-lag', // Default to Lagos, Nigeria
                          branch: 'main',
                        };

                        // Add repository data based on source type
                        if (selectedSourceType === 'git') {
                          createData.repository_url = formData.repositoryUrl;
                          createData.branch = formData.branch || 'main';
                        }
                        // For upload/docker sources, repository_url is optional and will be handled by backend

                        const newService = await applicationApi.createSimple(createData);
                        navigate(`/dashboard/services/${newService.id}`);
                      } catch (error) {
                        console.error('Failed to create service:', error);
                        setIsCreating(false);
                      }
                    }}
                    className="px-8 py-4 text-lg"
                  >
                    {isCreating ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Creating Service...
                      </>
                    ) : (
                      'Create Service'
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
