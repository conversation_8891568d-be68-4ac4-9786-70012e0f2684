{"hash": "956f4162", "configHash": "091ab3e2", "lockfileHash": "d88580e0", "browserHash": "59f64d40", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "68c28363", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "48a62eef", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ff76b770", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d5a1deed", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "76442df7", "needsInterop": false}, "@heroicons/react/20/solid": {"src": "../../@heroicons/react/20/solid/esm/index.js", "file": "@heroicons_react_20_solid.js", "fileHash": "5f4b4823", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "243f421b", "needsInterop": false}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "0f203e81", "needsInterop": false}, "@xterm/addon-fit": {"src": "../../@xterm/addon-fit/lib/addon-fit.js", "file": "@xterm_addon-fit.js", "fileHash": "34d5efd2", "needsInterop": true}, "@xterm/addon-search": {"src": "../../@xterm/addon-search/lib/addon-search.js", "file": "@xterm_addon-search.js", "fileHash": "5f92e204", "needsInterop": true}, "@xterm/addon-web-links": {"src": "../../@xterm/addon-web-links/lib/addon-web-links.js", "file": "@xterm_addon-web-links.js", "fileHash": "cd72bc98", "needsInterop": true}, "@xterm/xterm": {"src": "../../@xterm/xterm/lib/xterm.js", "file": "@xterm_xterm.js", "fileHash": "0137df8b", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "a42401b5", "needsInterop": false}, "chart.js": {"src": "../../chart.js/dist/chart.js", "file": "chart__js.js", "fileHash": "afc8d6f4", "needsInterop": false}, "react-chartjs-2": {"src": "../../react-chartjs-2/dist/index.js", "file": "react-chartjs-2.js", "fileHash": "221ba082", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "eced5b16", "needsInterop": true}, "react-hotkeys-hook": {"src": "../../react-hotkeys-hook/packages/react-hotkeys-hook/dist/index.js", "file": "react-hotkeys-hook.js", "fileHash": "261c12be", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "ced34e85", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "128fef83", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "5e974b01", "needsInterop": false}}, "chunks": {"chunk-V3UIB5FC": {"file": "chunk-V3UIB5FC.js"}, "chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}