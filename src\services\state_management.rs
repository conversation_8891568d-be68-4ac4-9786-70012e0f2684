use crate::{
    models::{
        UserState, UserEnvironmentState, PaymentStatus, UserResourceUsage,
        ActivityMonitorConfig, SharedHostingError
    },
    services::{ServiceResult, ServiceError},
    database::Database,
    config::Config,
};
use chrono::{DateTime, Utc, Duration};
use std::collections::HashMap;
use tracing::{info, warn, error, instrument};
use mongodb::bson;
use tokio::process::Command;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateTransitionRule {
    pub from_state: UserState,
    pub to_state: UserState,
    pub condition: TransitionCondition,
    pub priority: u32,
    pub auto_execute: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransitionCondition {
    InactivityThreshold { minutes: u32 },
    PaymentOverdue { days: u32 },
    ResourceExceeded { cpu_percent: f64, memory_percent: f64 },
    ManualTrigger,
    ActivityDetected,
    PaymentReceived,
    AdminOverride,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateTransitionEvent {
    pub user_id: String,
    pub from_state: UserState,
    pub to_state: UserState,
    pub reason: String,
    pub triggered_by: TransitionTrigger,
    pub timestamp: DateTime<Utc>,
    pub execution_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransitionTrigger {
    AutomaticInactivity,
    AutomaticPayment,
    AutomaticResource,
    ManualUser,
    ManualAdmin,
    SystemOptimization,
}

pub struct StateManagementService {
    database: Database,
    config: Config,
    transition_rules: Vec<StateTransitionRule>,
    activity_config: ActivityMonitorConfig,
}

impl StateManagementService {
    pub fn new(database: &Database, config: &Config) -> Self {
        let transition_rules = Self::create_default_transition_rules();
        let activity_config = ActivityMonitorConfig {
            sleep_threshold_minutes: 30,
            cold_threshold_hours: 24,
            check_interval_seconds: 300,
            auto_state_transitions: true,
            payment_integration_enabled: true,
        };

        Self {
            database: database.clone(),
            config: config.clone(),
            transition_rules,
            activity_config,
        }
    }

    /// Create intelligent state transition rules
    fn create_default_transition_rules() -> Vec<StateTransitionRule> {
        vec![
            // Hot to Sleep: 30 minutes of inactivity
            StateTransitionRule {
                from_state: UserState::Hot,
                to_state: UserState::Sleep,
                condition: TransitionCondition::InactivityThreshold { minutes: 30 },
                priority: 1,
                auto_execute: true,
            },
            // Sleep to Cold: 24 hours of inactivity + payment issues
            StateTransitionRule {
                from_state: UserState::Sleep,
                to_state: UserState::Cold,
                condition: TransitionCondition::PaymentOverdue { days: 1 },
                priority: 2,
                auto_execute: true,
            },
            // Sleep to Hot: Activity detected
            StateTransitionRule {
                from_state: UserState::Sleep,
                to_state: UserState::Hot,
                condition: TransitionCondition::ActivityDetected,
                priority: 3,
                auto_execute: true,
            },
            // Cold to Hot: Payment received
            StateTransitionRule {
                from_state: UserState::Cold,
                to_state: UserState::Hot,
                condition: TransitionCondition::PaymentReceived,
                priority: 4,
                auto_execute: true,
            },
            // Hot to Cold: Resource abuse
            StateTransitionRule {
                from_state: UserState::Hot,
                to_state: UserState::Cold,
                condition: TransitionCondition::ResourceExceeded { 
                    cpu_percent: 95.0, 
                    memory_percent: 98.0 
                },
                priority: 5,
                auto_execute: false, // Requires admin review
            },
        ]
    }

    /// Execute state transition with intelligent decision making
    #[instrument(skip(self))]
    pub async fn execute_state_transition(
        &self,
        user_id: &str,
        to_state: UserState,
        trigger: TransitionTrigger,
        reason: Option<String>,
    ) -> ServiceResult<StateTransitionEvent> {
        let start_time = std::time::Instant::now();
        
        // Get current user state
        let current_state = self.get_user_current_state(user_id).await?;
        
        // Validate transition
        if !self.is_valid_transition(&current_state, &to_state, &trigger) {
            return Err(ServiceError::Validation(format!(
                "Invalid state transition from {:?} to {:?} with trigger {:?}",
                current_state, to_state, trigger
            )));
        }

        info!("Executing state transition for user {}: {:?} -> {:?}", user_id, current_state, to_state);

        // Execute pre-transition hooks
        self.execute_pre_transition_hooks(user_id, &current_state, &to_state).await?;

        // Execute the actual state change
        self.execute_state_change_script(user_id, &to_state).await?;

        // Update database
        self.update_user_state_in_database(user_id, &to_state).await?;

        // Execute post-transition hooks
        self.execute_post_transition_hooks(user_id, &current_state, &to_state).await?;

        let execution_time = start_time.elapsed().as_millis() as u64;
        
        let event = StateTransitionEvent {
            user_id: user_id.to_string(),
            from_state: current_state,
            to_state: to_state.clone(),
            reason: reason.unwrap_or_else(|| format!("Triggered by {:?}", trigger)),
            triggered_by: trigger,
            timestamp: Utc::now(),
            execution_time_ms: execution_time,
        };

        // Log the transition event
        self.log_transition_event(&event).await?;

        info!("State transition completed for user {} in {}ms", user_id, execution_time);
        Ok(event)
    }

    /// Intelligent activity monitoring and automatic state transitions
    #[instrument(skip(self))]
    pub async fn run_activity_monitor(&self) -> ServiceResult<Vec<StateTransitionEvent>> {
        info!("Running intelligent activity monitor");
        
        let mut transitions = Vec::new();
        let all_users = self.get_all_user_states().await?;

        for user_state in all_users {
            // Check for applicable transition rules
            for rule in &self.transition_rules {
                if rule.from_state == user_state.current_state && rule.auto_execute {
                    if self.evaluate_transition_condition(&user_state, &rule.condition).await? {
                        match self.execute_state_transition(
                            &user_state.user_id,
                            rule.to_state.clone(),
                            self.condition_to_trigger(&rule.condition),
                            Some(format!("Automatic transition: {}", self.condition_description(&rule.condition))),
                        ).await {
                            Ok(event) => transitions.push(event),
                            Err(e) => error!("Failed to execute automatic transition for user {}: {}", user_state.user_id, e),
                        }
                    }
                }
            }
        }

        info!("Activity monitor completed. Executed {} transitions", transitions.len());
        Ok(transitions)
    }

    /// Evaluate if a transition condition is met
    async fn evaluate_transition_condition(
        &self,
        user_state: &UserEnvironmentState,
        condition: &TransitionCondition,
    ) -> ServiceResult<bool> {
        match condition {
            TransitionCondition::InactivityThreshold { minutes } => {
                let threshold = Utc::now() - Duration::minutes(*minutes as i64);
                Ok(user_state.last_activity < threshold)
            },
            TransitionCondition::PaymentOverdue { days } => {
                let threshold = Utc::now() - Duration::days(*days as i64);
                Ok(user_state.payment_status == PaymentStatus::Overdue || 
                   user_state.payment_status == PaymentStatus::Suspended)
            },
            TransitionCondition::ResourceExceeded { cpu_percent, memory_percent } => {
                Ok(user_state.resource_usage.cpu_usage_percent > *cpu_percent ||
                   (user_state.resource_usage.memory_usage_mb as f64 / 1024.0) > *memory_percent)
            },
            TransitionCondition::ActivityDetected => {
                let recent_threshold = Utc::now() - Duration::minutes(5);
                Ok(user_state.last_activity > recent_threshold)
            },
            TransitionCondition::PaymentReceived => {
                Ok(user_state.payment_status == PaymentStatus::Active)
            },
            _ => Ok(false), // Manual triggers don't auto-execute
        }
    }

    /// Intelligent load balancing based on user states
    #[instrument(skip(self))]
    pub async fn optimize_user_distribution(&self) -> ServiceResult<Vec<String>> {
        info!("Running intelligent user distribution optimization");
        
        let mut recommendations = Vec::new();
        let server_loads = self.get_server_load_distribution().await?;

        // Find servers with high cold/sleep user ratios
        for (server_id, load) in server_loads {
            let cold_sleep_ratio = (load.cold_users + load.sleep_users) as f64 / load.total_users as f64;
            
            if cold_sleep_ratio > 0.7 && load.hot_users < 20 {
                recommendations.push(format!(
                    "Server {} has {}% inactive users - consider migrating cold users to optimize hot user performance",
                    server_id, (cold_sleep_ratio * 100.0) as u32
                ));
            }
            
            if load.hot_users > 80 {
                recommendations.push(format!(
                    "Server {} is at {}% hot user capacity - consider load balancing",
                    server_id, load.hot_users
                ));
            }
        }

        Ok(recommendations)
    }

    /// Bulk state transitions for operational efficiency
    #[instrument(skip(self))]
    pub async fn bulk_state_transition(
        &self,
        user_ids: Vec<String>,
        to_state: UserState,
        reason: String,
    ) -> ServiceResult<(Vec<String>, Vec<String>)> {
        let mut successful = Vec::new();
        let mut failed = Vec::new();

        for user_id in user_ids {
            match self.execute_state_transition(
                &user_id,
                to_state.clone(),
                TransitionTrigger::ManualAdmin,
                Some(reason.clone()),
            ).await {
                Ok(_) => successful.push(user_id),
                Err(e) => {
                    error!("Bulk transition failed for user {}: {}", user_id, e);
                    failed.push(user_id);
                }
            }
        }

        info!("Bulk transition completed: {} successful, {} failed", successful.len(), failed.len());
        Ok((successful, failed))
    }

    // Helper methods
    fn is_valid_transition(&self, from: &UserState, to: &UserState, trigger: &TransitionTrigger) -> bool {
        match (from, to, trigger) {
            // Admin can override any transition
            (_, _, TransitionTrigger::ManualAdmin) => true,
            // Users can wake themselves up
            (UserState::Sleep, UserState::Hot, TransitionTrigger::ManualUser) => true,
            // Users can put themselves to sleep
            (UserState::Hot, UserState::Sleep, TransitionTrigger::ManualUser) => true,
            // Automatic transitions follow rules
            (UserState::Hot, UserState::Sleep, TransitionTrigger::AutomaticInactivity) => true,
            (UserState::Sleep, UserState::Hot, TransitionTrigger::AutomaticInactivity) => true,
            (UserState::Sleep, UserState::Cold, TransitionTrigger::AutomaticPayment) => true,
            (UserState::Cold, UserState::Hot, TransitionTrigger::AutomaticPayment) => true,
            // System optimization allows strategic moves
            (_, _, TransitionTrigger::SystemOptimization) => true,
            _ => false,
        }
    }

    fn condition_to_trigger(&self, condition: &TransitionCondition) -> TransitionTrigger {
        match condition {
            TransitionCondition::InactivityThreshold { .. } => TransitionTrigger::AutomaticInactivity,
            TransitionCondition::PaymentOverdue { .. } => TransitionTrigger::AutomaticPayment,
            TransitionCondition::PaymentReceived => TransitionTrigger::AutomaticPayment,
            TransitionCondition::ResourceExceeded { .. } => TransitionTrigger::AutomaticResource,
            TransitionCondition::ActivityDetected => TransitionTrigger::AutomaticInactivity,
            _ => TransitionTrigger::SystemOptimization,
        }
    }

    fn condition_description(&self, condition: &TransitionCondition) -> String {
        match condition {
            TransitionCondition::InactivityThreshold { minutes } => format!("{} minutes of inactivity", minutes),
            TransitionCondition::PaymentOverdue { days } => format!("Payment overdue for {} days", days),
            TransitionCondition::ResourceExceeded { cpu_percent, memory_percent } => {
                format!("Resource usage exceeded: CPU {}%, Memory {}%", cpu_percent, memory_percent)
            },
            TransitionCondition::ActivityDetected => "Activity detected".to_string(),
            TransitionCondition::PaymentReceived => "Payment received".to_string(),
            _ => "Manual trigger".to_string(),
        }
    }

    // Database and system integration methods (to be implemented)
    async fn get_user_current_state(&self, user_id: &str) -> ServiceResult<UserState> {
        // TODO: Implement database query
        Ok(UserState::Hot)
    }

    async fn get_all_user_states(&self) -> ServiceResult<Vec<UserEnvironmentState>> {
        // TODO: Implement database query
        Ok(vec![])
    }

    async fn update_user_state_in_database(&self, user_id: &str, state: &UserState) -> ServiceResult<()> {
        // TODO: Implement database update
        Ok(())
    }

    async fn execute_state_change_script(&self, user_id: &str, state: &UserState) -> ServiceResult<()> {
        let state_str = match state {
            UserState::Hot => "hot",
            UserState::Cold => "cold",
            UserState::Sleep => "sleep",
        };

        let output = Command::new("/opt/achidas/scripts/change-user-state.sh")
            .args(&[user_id, state_str])
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Failed to execute state change script: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("State change script failed: {}", stderr)));
        }

        Ok(())
    }

    async fn execute_pre_transition_hooks(&self, user_id: &str, from: &UserState, to: &UserState) -> ServiceResult<()> {
        // Implement pre-transition logic (backup, notifications, etc.)
        info!("Executing pre-transition hooks for user {}: {:?} -> {:?}", user_id, from, to);
        Ok(())
    }

    async fn execute_post_transition_hooks(&self, user_id: &str, from: &UserState, to: &UserState) -> ServiceResult<()> {
        // Implement post-transition logic (cleanup, notifications, etc.)
        info!("Executing post-transition hooks for user {}: {:?} -> {:?}", user_id, from, to);
        Ok(())
    }

    async fn log_transition_event(&self, event: &StateTransitionEvent) -> ServiceResult<()> {
        // TODO: Implement event logging to database
        info!("Transition event logged: {:?}", event);
        Ok(())
    }

    async fn get_server_load_distribution(&self) -> ServiceResult<HashMap<String, ServerLoad>> {
        // TODO: Implement server load query
        Ok(HashMap::new())
    }

    /// Get count of state transitions in the last hour for orchestration metrics
    pub async fn get_transitions_last_hour(&self) -> ServiceResult<u32> {
        let collection = self.database.collection::<StateTransitionEvent>("state_transitions");

        let one_hour_ago = Utc::now() - chrono::Duration::hours(1);
        let count = collection
            .count_documents(
                bson::doc! {
                    "timestamp": {
                        "$gte": one_hour_ago
                    }
                },
                None
            )
            .await
            .map_err(|e| ServiceError::Database(e))?;

        Ok(count as u32)
    }
}

#[derive(Debug, Clone)]
struct ServerLoad {
    total_users: u32,
    hot_users: u32,
    cold_users: u32,
    sleep_users: u32,
}
