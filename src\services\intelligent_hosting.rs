use crate::{
    config::Config,
    services::{ServiceError, ServiceResult},
    vultr::{VultrClient, CreateInstanceRequest},
};
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, instrument, warn, error};
use tokio::time::{sleep, Duration};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HostingTier {
    pub tier_type: HostingTierType,
    pub plans: Vec<HostingPlan>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HostingTierType {
    Shared,
    Dedicated,
    Enterprise,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HostingPlan {
    pub name: String,
    pub monthly_price_usd: f64,
    pub local_pricing: HashMap<String, f64>,
    pub resource_allocation: ResourceAllocation,
    pub burst_capability: Option<BurstCapability>,
    pub priority_weight: u32,
    pub target_users: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ResourceAllocation {
    pub cpu_shares: Option<u32>,
    pub memory_reservation: Option<String>,
    pub memory_limit: Option<String>,
    pub storage_limit: String,
    pub bandwidth_limit: String,
    pub domains: Option<u32>,
    pub databases: Option<u32>,
    pub vcpu: Option<u32>,
    pub memory_gb: Option<u32>,
    pub guaranteed_resources: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BurstCapability {
    pub cpu_burst: String,
    pub memory_burst: String,
    pub performance_multiplier: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerPool {
    pub pool_type: PoolType,
    pub server_type: String,
    pub cost_per_server: f64,
    pub max_users_per_server: u32,
    pub current_servers: Vec<Server>,
    pub auto_scaling_config: AutoScalingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize, Hash, Eq, PartialEq)]
pub enum PoolType {
    SharedHot,
    SharedCold,
    Dedicated,
    Enterprise,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Server {
    pub instance_id: String,
    pub server_type: String,
    pub pool_type: PoolType,
    pub current_users: u32,
    pub max_users: u32,
    pub cpu_utilization: f64,
    pub memory_utilization: f64,
    pub status: ServerStatus,
    pub region: String,
    pub monthly_cost: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServerStatus {
    Provisioning,
    Active,
    Inactive,
    Maintenance,
    Scaling,
    Terminating,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoScalingConfig {
    pub scale_up_trigger: String,
    pub scale_down_trigger: String,
    pub cooldown_period: String,
    pub enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserDeployment {
    pub user_id: String,
    pub plan: String,
    pub tier: HostingTierType,
    pub server_id: String,
    pub container_id: Option<String>,
    pub vm_id: Option<String>,
    pub status: DeploymentStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub resource_usage: ResourceUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeploymentStatus {
    Pending,
    Deploying,
    Active,
    Sleeping,
    Migrating,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub storage_usage: f64,
    pub bandwidth_usage: f64,
    pub last_activity: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerPoolStatus {
    pub pool_type: PoolType,
    pub total_servers: u32,
    pub active_servers: u32,
    pub total_users: u32,
    pub max_capacity: u32,
    pub average_cpu_utilization: f64,
    pub average_memory_utilization: f64,
    pub monthly_cost: f64,
    pub auto_scaling_enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfitAnalysis {
    pub total_revenue: f64,
    pub total_cost: f64,
    pub profit: f64,
    pub profit_margin: f64,
    pub plan_breakdown: Vec<PlanRevenue>,
    pub break_even_users: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlanRevenue {
    pub plan_name: String,
    pub user_count: u32,
    pub monthly_price: f64,
    pub total_revenue: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingAction {
    pub action_type: ScalingActionType,
    pub pool_type: PoolType,
    pub server_id: Option<String>,
    pub reason: String,
    pub estimated_cost: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScalingActionType {
    ScaleUp,
    ScaleDown,
    AddServer,
    RemoveServer,
    MigrateUsers,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrServerInfo {
    pub instance_id: String,
    pub label: String,
    pub plan: String,
    pub region: String,
    pub status: String,
    pub main_ip: String,
    pub monthly_cost: f64,
    pub recommended_pool: PoolType,
    pub max_users: u32,
    pub created_date: String,
    pub suitable_for_achidas: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerSetupInfo {
    pub instance_id: String,
    pub setup_script: String,
    pub ssh_command: String,
    pub next_steps: Vec<String>,
}

#[derive(Clone)]
pub struct IntelligentHostingService {
    vultr_client: VultrClient,
    config: Config,
    server_pools: HashMap<PoolType, ServerPool>,
}

impl IntelligentHostingService {
    pub fn new(vultr_client: VultrClient, config: Config) -> Self {
        let mut server_pools = HashMap::new();
        
        // Initialize server pools
        server_pools.insert(PoolType::SharedHot, ServerPool {
            pool_type: PoolType::SharedHot,
            server_type: "vhf-1c-1gb".to_string(),
            cost_per_server: 6.00,
            max_users_per_server: 100,
            current_servers: Vec::new(),
            auto_scaling_config: AutoScalingConfig {
                scale_up_trigger: "CPU >80% for 5min OR Memory >85% for 3min OR Users >90".to_string(),
                scale_down_trigger: "CPU <30% for 15min AND Memory <40% for 15min AND Users <50".to_string(),
                cooldown_period: "10 minutes".to_string(),
                enabled: true,
            },
        });

        server_pools.insert(PoolType::SharedCold, ServerPool {
            pool_type: PoolType::SharedCold,
            server_type: "vc2-1c-1gb".to_string(),
            cost_per_server: 5.00,
            max_users_per_server: 120,
            current_servers: Vec::new(),
            auto_scaling_config: AutoScalingConfig {
                scale_up_trigger: "CPU >75% for 10min OR Memory >80% for 5min".to_string(),
                scale_down_trigger: "CPU <20% for 20min AND Memory <30% for 20min".to_string(),
                cooldown_period: "15 minutes".to_string(),
                enabled: true,
            },
        });

        Self {
            vultr_client,
            config,
            server_pools,
        }
    }

    #[instrument(skip(self))]
    pub async fn import_existing_server(&self, instance_id: &str, pool_type: PoolType) -> ServiceResult<Server> {
        info!("Importing existing Vultr server {} into {} pool", instance_id, pool_type_to_string(&pool_type));

        // Get server details from Vultr
        let instance = self
            .vultr_client
            .get_instance_detailed(instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to get instance details: {}", e)))?;

        // Determine server specifications based on plan
        let (server_type, monthly_cost, max_users) = match instance.plan.as_str() {
            "vhf-1c-1gb" => ("vhf-1c-1gb".to_string(), 6.00, 100),
            "vc2-1c-1gb" => ("vc2-1c-1gb".to_string(), 5.00, 120),
            "vc2-2c-4gb" => ("vc2-2c-4gb".to_string(), 12.00, 1),
            "vc2-4c-8gb" => ("vc2-4c-8gb".to_string(), 24.00, 1),
            "vc2-8c-16gb" => ("vc2-8c-16gb".to_string(), 48.00, 1),
            _ => (instance.plan.clone(), 10.00, 50), // Default fallback
        };

        let server = Server {
            instance_id: instance.id.clone(),
            server_type,
            pool_type: pool_type.clone(),
            current_users: 0,
            max_users,
            cpu_utilization: 0.0,
            memory_utilization: 0.0,
            status: if instance.status == "active" { ServerStatus::Active } else { ServerStatus::Provisioning },
            region: instance.region.clone(),
            monthly_cost,
        };

        info!("Server imported successfully: {} ({})", instance.id, instance.plan);
        Ok(server)
    }

    #[instrument(skip(self))]
    pub async fn setup_imported_server(&self, instance_id: &str, pool_type: &PoolType) -> ServiceResult<()> {
        info!("Setting up imported server {} for {} hosting", instance_id, pool_type_to_string(pool_type));

        // Get server details
        let instance = self
            .vultr_client
            .get_instance_detailed(instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to get instance details: {}", e)))?;

        if instance.status != "active" {
            return Err(ServiceError::BadRequest("Server must be active before setup".to_string()));
        }

        // Generate setup script based on pool type
        let setup_script = match pool_type {
            PoolType::SharedHot | PoolType::SharedCold => self.generate_shared_hosting_setup_script().await?,
            PoolType::Dedicated => self.generate_dedicated_setup_script().await?,
            PoolType::Enterprise => self.generate_enterprise_setup_script().await?,
        };

        info!("Server setup script generated. Manual execution required on server: {}", instance_id);
        info!("Server IP: {}", instance.main_ip.unwrap_or_else(|| "N/A".to_string()));
        info!("Setup script ready for deployment");

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_vultr_servers(&self) -> ServiceResult<Vec<VultrServerInfo>> {
        info!("Fetching all Vultr servers for import");

        let instances = self
            .vultr_client
            .list_instances_detailed()
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to list instances: {}", e)))?;

        let mut servers = Vec::new();
        for instance in instances {
            let (recommended_pool, max_users, monthly_cost) = match instance.plan.as_str() {
                "vhf-1c-1gb" => (PoolType::SharedHot, 100, 6.00),
                "vc2-1c-1gb" => (PoolType::SharedCold, 120, 5.00),
                "vc2-2c-4gb" | "vc2-4c-8gb" | "vc2-8c-16gb" => (PoolType::Dedicated, 1, 12.00),
                _ => (PoolType::SharedCold, 50, 10.00),
            };

            let plan = instance.plan.clone();
            let suitable_for_achidas = is_suitable_for_achidas(&plan);

            servers.push(VultrServerInfo {
                instance_id: instance.id,
                label: if instance.label.is_empty() { "Unlabeled".to_string() } else { instance.label },
                plan,
                region: instance.region,
                status: instance.status,
                main_ip: instance.main_ip.unwrap_or_else(|| "N/A".to_string()),
                monthly_cost,
                recommended_pool,
                max_users,
                created_date: instance.date_created,
                suitable_for_achidas,
            });
        }

        info!("Found {} Vultr servers", servers.len());
        Ok(servers)
    }

    #[instrument(skip(self))]
    pub async fn get_server_details(&self, instance_id: &str) -> ServiceResult<VultrServerInfo> {
        info!("Getting details for Vultr server: {}", instance_id);

        let instance = self
            .vultr_client
            .get_instance_detailed(instance_id)
            .await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to get instance details: {}", e)))?;

        let (recommended_pool, max_users, monthly_cost) = match instance.plan.as_str() {
            "vhf-1c-1gb" => (PoolType::SharedHot, 100, 6.00),
            "vc2-1c-1gb" => (PoolType::SharedCold, 120, 5.00),
            "vc2-2c-4gb" => (PoolType::Dedicated, 1, 12.00),
            "vc2-4c-8gb" => (PoolType::Dedicated, 1, 24.00),
            "vc2-8c-16gb" => (PoolType::Dedicated, 1, 48.00),
            "vc2-16c-32gb" => (PoolType::Enterprise, 1, 96.00),
            "vc2-32c-64gb" => (PoolType::Enterprise, 1, 192.00),
            _ => (PoolType::SharedCold, 50, 10.00),
        };

        let server_info = VultrServerInfo {
            instance_id: instance.id,
            label: if instance.label.is_empty() { "Unlabeled".to_string() } else { instance.label.clone() },
            plan: instance.plan.clone(),
            region: instance.region,
            status: instance.status,
            main_ip: instance.main_ip.unwrap_or_else(|| "N/A".to_string()),
            monthly_cost,
            recommended_pool,
            max_users,
            created_date: instance.date_created,
            suitable_for_achidas: is_suitable_for_achidas(&instance.plan),
        };

        info!("Server details retrieved: {} ({})", server_info.instance_id, server_info.plan);
        Ok(server_info)
    }

    #[instrument(skip(self))]
    pub async fn deploy_user(&self, user_id: &str, plan: &str, region: &str) -> ServiceResult<UserDeployment> {
        info!("Deploying user {} with plan {} in region {}", user_id, plan, region);

        let hosting_plan = self.get_hosting_plan(plan)?;
        let tier = self.determine_hosting_tier(plan);
        
        match tier {
            HostingTierType::Shared => {
                self.deploy_shared_user(user_id, &hosting_plan, region).await
            },
            HostingTierType::Dedicated => {
                self.deploy_dedicated_user(user_id, &hosting_plan, region).await
            },
            HostingTierType::Enterprise => {
                self.deploy_enterprise_user(user_id, &hosting_plan, region).await
            },
        }
    }

    #[instrument(skip(self))]
    async fn deploy_shared_user(&self, user_id: &str, plan: &HostingPlan, region: &str) -> ServiceResult<UserDeployment> {
        // Find best server in hot pool
        let server = self.find_best_server_for_user(&PoolType::SharedHot, region).await?;
        
        // Deploy container with plan-specific resources
        let container_config = self.generate_container_config(plan);
        let container_id = self.deploy_container(user_id, &container_config, &server.instance_id).await?;

        Ok(UserDeployment {
            user_id: user_id.to_string(),
            plan: plan.name.clone(),
            tier: HostingTierType::Shared,
            server_id: server.instance_id,
            container_id: Some(container_id),
            vm_id: None,
            status: DeploymentStatus::Active,
            created_at: chrono::Utc::now(),
            resource_usage: ResourceUsage {
                cpu_usage: 0.0,
                memory_usage: 0.0,
                storage_usage: 0.0,
                bandwidth_usage: 0.0,
                last_activity: chrono::Utc::now(),
            },
        })
    }

    #[instrument(skip(self))]
    async fn deploy_dedicated_user(&self, user_id: &str, plan: &HostingPlan, region: &str) -> ServiceResult<UserDeployment> {
        // Create dedicated VM
        let vm_specs = plan.resource_allocation.clone();
        let vm_id = self.create_dedicated_vm(user_id, &vm_specs, region).await?;

        Ok(UserDeployment {
            user_id: user_id.to_string(),
            plan: plan.name.clone(),
            tier: HostingTierType::Dedicated,
            server_id: vm_id.clone(),
            container_id: None,
            vm_id: Some(vm_id),
            status: DeploymentStatus::Active,
            created_at: chrono::Utc::now(),
            resource_usage: ResourceUsage {
                cpu_usage: 0.0,
                memory_usage: 0.0,
                storage_usage: 0.0,
                bandwidth_usage: 0.0,
                last_activity: chrono::Utc::now(),
            },
        })
    }

    #[instrument(skip(self))]
    async fn deploy_enterprise_user(&self, user_id: &str, plan: &HostingPlan, region: &str) -> ServiceResult<UserDeployment> {
        // Create enterprise-grade server
        let server_specs = plan.resource_allocation.clone();
        let server_id = self.create_enterprise_server(user_id, &server_specs, region).await?;

        Ok(UserDeployment {
            user_id: user_id.to_string(),
            plan: plan.name.clone(),
            tier: HostingTierType::Enterprise,
            server_id: server_id.clone(),
            container_id: None,
            vm_id: Some(server_id),
            status: DeploymentStatus::Active,
            created_at: chrono::Utc::now(),
            resource_usage: ResourceUsage {
                cpu_usage: 0.0,
                memory_usage: 0.0,
                storage_usage: 0.0,
                bandwidth_usage: 0.0,
                last_activity: chrono::Utc::now(),
            },
        })
    }

    pub async fn generate_shared_hosting_setup_script(&self) -> ServiceResult<String> {
        let user_data = r#"#!/bin/bash
set -e

echo "Setting up Achidas Multi-Tenant Shared Hosting Server..."

# Update system
apt-get update
apt-get upgrade -y

# Install required packages for multi-tenant isolation
apt-get install -y \
    docker.io \
    docker-compose \
    quota \
    quotatool \
    cgroup-tools \
    debootstrap \
    schroot \
    openssh-server \
    fail2ban \
    ufw \
    jq \
    htop \
    iotop

# Install Docker (latest version)
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl enable docker
systemctl start docker

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Configure system for advanced shared hosting with isolation
echo 'vm.overcommit_memory = 1' >> /etc/sysctl.conf
echo 'vm.overcommit_ratio = 150' >> /etc/sysctl.conf
echo 'vm.swappiness = 10' >> /etc/sysctl.conf
echo 'fs.inotify.max_user_watches = 524288' >> /etc/sysctl.conf
echo 'fs.inotify.max_user_instances = 512' >> /etc/sysctl.conf
echo 'kernel.pid_max = 4194304' >> /etc/sysctl.conf
sysctl -p

# Enable cgroups v2 for better resource control
echo 'GRUB_CMDLINE_LINUX="systemd.unified_cgroup_hierarchy=1"' >> /etc/default/grub
update-grub

# Create Achidas networks for different states
docker network create achidas-shared || true
docker network create achidas-hot || true
docker network create achidas-cold || true

# Setup multi-tenant file system structure
mkdir -p /opt/achidas/{users,ssh-keys,scripts,logs,monitoring,backups}
mkdir -p /opt/achidas/users/{hot,cold,sleep}
mkdir -p /var/lib/achidas/{quotas,states,activity}

# Setup quota system for user directories
echo '/opt/achidas/users /opt/achidas/users ext4 defaults,usrquota,grpquota 0 0' >> /etc/fstab
mount -o remount /opt/achidas/users
quotacheck -cum /opt/achidas/users
quotaon /opt/achidas/users

# Install Traefik
mkdir -p /opt/traefik
cat > /opt/traefik/docker-compose.yml << 'EOF'
version: '3.8'
services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/traefik.yml:ro
      - ./letsencrypt:/letsencrypt
    networks:
      - achidas-shared
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(\`traefik.achidas.local\`)"
      - "traefik.http.routers.dashboard.service=api@internal"

networks:
  achidas-shared:
    external: true
EOF

cat > /opt/traefik/traefik.yml << 'EOF'
global:
  checkNewVersion: false
  sendAnonymousUsage: false

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "achidas-shared"

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web

api:
  dashboard: true
  insecure: true
EOF

cd /opt/traefik && docker-compose up -d

# Setup monitoring
mkdir -p /opt/monitoring
cat > /opt/monitoring/docker-compose.yml << 'EOF'
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - achidas-shared

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    networks:
      - achidas-shared

networks:
  achidas-shared:
    external: true
EOF

cat > /opt/monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
  
  - job_name: 'docker'
    static_configs:
      - targets: ['localhost:9323']
EOF

cd /opt/monitoring && docker-compose up -d

# Create advanced user management scripts
cat > /opt/achidas/scripts/create-user-environment.sh << 'EOF'
#!/bin/bash
USER_ID=$1
USER_STATE=${2:-"hot"}  # hot, cold, sleep
PLAN=$3
SSH_KEY=$4

echo "Creating isolated environment for user: $USER_ID (state: $USER_STATE)"

# Create user-specific directory structure
USER_DIR="/opt/achidas/users/$USER_STATE/$USER_ID"
mkdir -p "$USER_DIR"/{app,data,logs,tmp,ssh}
chmod 750 "$USER_DIR"
chown root:docker "$USER_DIR"

# Set up user quotas (1GB default, adjustable by plan)
case $PLAN in
  starter) QUOTA="500M" ;;
  nano) QUOTA="1G" ;;
  micro) QUOTA="2G" ;;
  small) QUOTA="5G" ;;
  business) QUOTA="10G" ;;
  *) QUOTA="1G" ;;
esac

# Apply quota to user directory
setquota -u $(id -u docker) 0 ${QUOTA%?}000000 0 0 /opt/achidas/users

# Create user-specific SSH key mapping
if [ -n "$SSH_KEY" ]; then
    echo "$SSH_KEY" > "/opt/achidas/ssh-keys/$USER_ID.pub"
    chmod 600 "/opt/achidas/ssh-keys/$USER_ID.pub"
fi

# Initialize user state tracking
echo "{\"user_id\":\"$USER_ID\",\"state\":\"$USER_STATE\",\"created\":\"$(date -Iseconds)\",\"last_activity\":\"$(date -Iseconds)\",\"plan\":\"$PLAN\"}" > "/var/lib/achidas/states/$USER_ID.json"

echo "User environment created successfully"
EOF

chmod +x /opt/achidas/scripts/create-user-environment.sh

cat > /opt/achidas/scripts/deploy-user.sh << 'EOF'
#!/bin/bash
USER_ID=$1
PLAN=$2
IMAGE=$3
USER_STATE=${4:-"hot"}

# Load user state
STATE_FILE="/var/lib/achidas/states/$USER_ID.json"
if [ ! -f "$STATE_FILE" ]; then
    echo "Error: User $USER_ID not found. Create user environment first."
    exit 1
fi

# Check if user is in cold or sleep state
CURRENT_STATE=$(jq -r '.state' "$STATE_FILE")
if [ "$CURRENT_STATE" = "cold" ]; then
    echo "User $USER_ID is in cold state. Deployment blocked."
    exit 1
fi

if [ "$CURRENT_STATE" = "sleep" ]; then
    echo "Waking up user $USER_ID from sleep state..."
    /opt/achidas/scripts/change-user-state.sh "$USER_ID" "hot"
fi

# Set resource limits based on plan and state
case $PLAN in
  starter) CPU_SHARES=256; MEM_RES=8m; MEM_LIMIT=64m ;;
  nano) CPU_SHARES=512; MEM_RES=10m; MEM_LIMIT=128m ;;
  micro) CPU_SHARES=1024; MEM_RES=20m; MEM_LIMIT=256m ;;
  small) CPU_SHARES=2048; MEM_RES=40m; MEM_LIMIT=512m ;;
  business) CPU_SHARES=4096; MEM_RES=80m; MEM_LIMIT=1024m ;;
esac

# Determine network based on user state
NETWORK="achidas-$USER_STATE"
USER_DIR="/opt/achidas/users/$USER_STATE/$USER_ID"

# Deploy container with advanced isolation
docker run -d \
  --cpu-shares=$CPU_SHARES \
  --memory-reservation=$MEM_RES \
  --memory=$MEM_LIMIT \
  --memory-swap=0 \
  --name=user-$USER_ID \
  --network=$NETWORK \
  --restart=unless-stopped \
  --security-opt=no-new-privileges:true \
  --cap-drop=ALL \
  --cap-add=CHOWN \
  --cap-add=SETUID \
  --cap-add=SETGID \
  --read-only \
  --tmpfs /tmp:rw,noexec,nosuid,size=100m \
  -v "$USER_DIR/app":/app:rw \
  -v "$USER_DIR/data":/data:rw \
  -v "$USER_DIR/logs":/logs:rw \
  -v "$USER_DIR/tmp":/app/tmp:rw \
  --label=traefik.enable=true \
  --label=traefik.http.routers.user-$USER_ID.rule=Host\(\`$USER_ID.achidas.com\`\) \
  --label=traefik.http.services.user-$USER_ID.loadbalancer.server.port=8080 \
  --label=achidas.user_id=$USER_ID \
  --label=achidas.plan=$PLAN \
  --label=achidas.state=$USER_STATE \
  $IMAGE

# Update last activity
jq '.last_activity = "'$(date -Iseconds)'"' "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

echo "User $USER_ID deployed successfully in $USER_STATE state"
EOF

chmod +x /opt/achidas/scripts/deploy-user.sh

cat > /opt/achidas/scripts/change-user-state.sh << 'EOF'
#!/bin/bash
USER_ID=$1
NEW_STATE=$2  # hot, cold, sleep

STATE_FILE="/var/lib/achidas/states/$USER_ID.json"
if [ ! -f "$STATE_FILE" ]; then
    echo "Error: User $USER_ID not found"
    exit 1
fi

OLD_STATE=$(jq -r '.state' "$STATE_FILE")
echo "Changing user $USER_ID state from $OLD_STATE to $NEW_STATE"

# Stop existing container if running
docker stop "user-$USER_ID" 2>/dev/null || true
docker rm "user-$USER_ID" 2>/dev/null || true

# Move user directory to new state location
OLD_DIR="/opt/achidas/users/$OLD_STATE/$USER_ID"
NEW_DIR="/opt/achidas/users/$NEW_STATE/$USER_ID"

if [ -d "$OLD_DIR" ] && [ "$OLD_STATE" != "$NEW_STATE" ]; then
    mkdir -p "/opt/achidas/users/$NEW_STATE"
    mv "$OLD_DIR" "$NEW_DIR"
fi

# Update state file
jq '.state = "'$NEW_STATE'" | .last_state_change = "'$(date -Iseconds)'"' "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

# Apply state-specific actions
case $NEW_STATE in
    "cold")
        echo "User $USER_ID moved to cold state - access disabled"
        # Disable SSH access, stop services
        ;;
    "sleep")
        echo "User $USER_ID moved to sleep state - hibernated"
        # Compress user data, minimal resource usage
        ;;
    "hot")
        echo "User $USER_ID moved to hot state - active"
        # Enable full access and services
        ;;
esac

echo "State change completed"
EOF

chmod +x /opt/achidas/scripts/change-user-state.sh

cat > /opt/achidas/scripts/ssh-key-auth.sh << 'EOF'
#!/bin/bash
# SSH Key Authentication Handler
# This script is called when a user attempts SSH access

USER_ID=$1
PROVIDED_KEY=$2

echo "SSH authentication attempt for user: $USER_ID"

# Check if user exists and get state
STATE_FILE="/var/lib/achidas/states/$USER_ID.json"
if [ ! -f "$STATE_FILE" ]; then
    echo "User $USER_ID not found"
    exit 1
fi

USER_STATE=$(jq -r '.state' "$STATE_FILE")

# Block access for cold users
if [ "$USER_STATE" = "cold" ]; then
    echo "Access denied: User $USER_ID is in cold state"
    exit 1
fi

# Check SSH key
USER_KEY_FILE="/opt/achidas/ssh-keys/$USER_ID.pub"
if [ ! -f "$USER_KEY_FILE" ]; then
    echo "No SSH key found for user $USER_ID"
    exit 1
fi

# Validate provided key against stored key
if ssh-keygen -l -f "$USER_KEY_FILE" | grep -q "$(ssh-keygen -l -f <(echo "$PROVIDED_KEY"))"; then
    echo "SSH key validated for user $USER_ID"

    # Update last activity
    jq '.last_activity = "'$(date -Iseconds)'"' "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"

    # Wake up sleeping users
    if [ "$USER_STATE" = "sleep" ]; then
        echo "Waking up user $USER_ID from sleep state"
        /opt/achidas/scripts/change-user-state.sh "$USER_ID" "hot"
    fi

    # Grant access to user's chroot environment
    echo "Access granted to user $USER_ID"
    exec chroot "/opt/achidas/users/$USER_STATE/$USER_ID" /bin/bash
else
    echo "SSH key validation failed for user $USER_ID"
    exit 1
fi
EOF

chmod +x /opt/achidas/scripts/ssh-key-auth.sh

cat > /opt/achidas/scripts/activity-monitor.sh << 'EOF'
#!/bin/bash
# Activity monitoring daemon for automatic state transitions

echo "Starting Achidas activity monitor..."

while true; do
    # Check all users for inactivity
    for state_file in /var/lib/achidas/states/*.json; do
        if [ ! -f "$state_file" ]; then
            continue
        fi

        USER_ID=$(basename "$state_file" .json)
        LAST_ACTIVITY=$(jq -r '.last_activity' "$state_file")
        CURRENT_STATE=$(jq -r '.state' "$state_file")

        # Calculate inactivity time
        LAST_TIMESTAMP=$(date -d "$LAST_ACTIVITY" +%s)
        CURRENT_TIMESTAMP=$(date +%s)
        INACTIVE_SECONDS=$((CURRENT_TIMESTAMP - LAST_TIMESTAMP))

        # Auto-transition to sleep after 30 minutes of inactivity
        if [ $INACTIVE_SECONDS -gt 1800 ] && [ "$CURRENT_STATE" = "hot" ]; then
            echo "Moving inactive user $USER_ID to sleep state"
            /opt/achidas/scripts/change-user-state.sh "$USER_ID" "sleep"
        fi

        # Auto-transition to cold after 24 hours of inactivity (for non-paying users)
        if [ $INACTIVE_SECONDS -gt 86400 ] && [ "$CURRENT_STATE" = "sleep" ]; then
            # Check payment status (this would integrate with your billing system)
            echo "Checking payment status for user $USER_ID"
            # For now, just log - integrate with billing API later
        fi
    done

    # Sleep for 5 minutes before next check
    sleep 300
done
EOF

chmod +x /opt/achidas/scripts/activity-monitor.sh

# Create systemd service for activity monitor
cat > /etc/systemd/system/achidas-monitor.service << 'EOF'
[Unit]
Description=Achidas Activity Monitor
After=docker.service

[Service]
Type=simple
ExecStart=/opt/achidas/scripts/activity-monitor.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl enable achidas-monitor.service
systemctl start achidas-monitor.service

# Log completion
echo "Achidas multi-tenant shared hosting server setup completed" > /var/log/achidas-setup.log
echo "Features enabled:" >> /var/log/achidas-setup.log
echo "- Multi-tenant file isolation" >> /var/log/achidas-setup.log
echo "- Hot/Cold/Sleep state management" >> /var/log/achidas-setup.log
echo "- SSH key authentication" >> /var/log/achidas-setup.log
echo "- Automatic activity monitoring" >> /var/log/achidas-setup.log
echo "- Resource quotas and limits" >> /var/log/achidas-setup.log
date >> /var/log/achidas-setup.log
"#;

        Ok(user_data.to_string())
    }

    pub async fn generate_dedicated_setup_script(&self) -> ServiceResult<String> {
        let setup_script = r#"#!/bin/bash
set -e

echo "Setting up Achidas Dedicated Hosting Server..."

# Update system
apt-get update
apt-get upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl enable docker
systemctl start docker

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Configure firewall for dedicated hosting
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8080/tcp
ufw --force enable

# Create dedicated hosting directory
mkdir -p /opt/achidas-dedicated
cd /opt/achidas-dedicated

# Setup monitoring
mkdir -p monitoring
cat > monitoring/docker-compose.yml << 'EOF'
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
EOF

# Create user deployment script
cat > deploy-dedicated-user.sh << 'EOF'
#!/bin/bash
USER_ID=$1
IMAGE=$2
PORT=${3:-8080}

docker run -d \
  --name=dedicated-user-$USER_ID \
  --restart=unless-stopped \
  -p $PORT:8080 \
  -v /opt/achidas-dedicated/users/$USER_ID:/app/data \
  $IMAGE
EOF

chmod +x deploy-dedicated-user.sh

echo "Achidas dedicated hosting server setup completed"
echo "Server is ready for dedicated user deployments"
"#;

        Ok(setup_script.to_string())
    }

    pub async fn generate_enterprise_setup_script(&self) -> ServiceResult<String> {
        let setup_script = r#"#!/bin/bash
set -e

echo "Setting up Achidas Enterprise Hosting Server..."

# Update system
apt-get update
apt-get upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl enable docker
systemctl start docker

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Install Kubernetes (for enterprise features)
curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -
echo "deb https://apt.kubernetes.io/ kubernetes-xenial main" > /etc/apt/sources.list.d/kubernetes.list
apt-get update
apt-get install -y kubelet kubeadm kubectl
apt-mark hold kubelet kubeadm kubectl

# Configure enterprise-grade security
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 6443/tcp  # Kubernetes API
ufw allow 10250/tcp # kubelet
ufw --force enable

# Create enterprise hosting directory
mkdir -p /opt/achidas-enterprise
cd /opt/achidas-enterprise

# Setup comprehensive monitoring
mkdir -p monitoring
cat > monitoring/docker-compose.yml << 'EOF'
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=achidas123

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"

  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
EOF

# Create enterprise deployment script
cat > deploy-enterprise-user.sh << 'EOF'
#!/bin/bash
USER_ID=$1
IMAGE=$2
RESOURCES=${3:-"high"}

case $RESOURCES in
  high)
    CPU_LIMIT="4"
    MEMORY_LIMIT="8g"
    ;;
  medium)
    CPU_LIMIT="2"
    MEMORY_LIMIT="4g"
    ;;
  *)
    CPU_LIMIT="1"
    MEMORY_LIMIT="2g"
    ;;
esac

docker run -d \
  --name=enterprise-user-$USER_ID \
  --restart=unless-stopped \
  --cpus=$CPU_LIMIT \
  --memory=$MEMORY_LIMIT \
  -v /opt/achidas-enterprise/users/$USER_ID:/app/data \
  $IMAGE
EOF

chmod +x deploy-enterprise-user.sh

echo "Achidas enterprise hosting server setup completed"
echo "Server is ready for enterprise deployments with SLA guarantees"
"#;

        Ok(setup_script.to_string())
    }

    #[instrument(skip(self))]
    pub async fn get_all_hosting_plans(&self) -> ServiceResult<Vec<HostingPlan>> {
        Ok(vec![
            // Shared Hosting Plans
            self.get_hosting_plan("starter")?,
            self.get_hosting_plan("nano")?,
            self.get_hosting_plan("micro")?,
            self.get_hosting_plan("small")?,
            self.get_hosting_plan("business")?,
            // Dedicated Hosting Plans
            self.get_hosting_plan("dedicated_small")?,
            self.get_hosting_plan("dedicated_medium")?,
            self.get_hosting_plan("dedicated_large")?,
            // Enterprise Hosting Plans
            self.get_hosting_plan("enterprise_standard")?,
            self.get_hosting_plan("enterprise_premium")?,
        ])
    }

    #[instrument(skip(self))]
    pub async fn get_server_pools_status(&self) -> ServiceResult<HashMap<PoolType, ServerPoolStatus>> {
        let mut status = HashMap::new();

        for (pool_type, pool) in &self.server_pools {
            let pool_status = ServerPoolStatus {
                pool_type: pool_type.clone(),
                total_servers: pool.current_servers.len() as u32,
                active_servers: pool.current_servers.iter().filter(|s| matches!(s.status, ServerStatus::Active)).count() as u32,
                total_users: pool.current_servers.iter().map(|s| s.current_users).sum(),
                max_capacity: pool.current_servers.len() as u32 * pool.max_users_per_server,
                average_cpu_utilization: pool.current_servers.iter().map(|s| s.cpu_utilization).sum::<f64>() / pool.current_servers.len().max(1) as f64,
                average_memory_utilization: pool.current_servers.iter().map(|s| s.memory_utilization).sum::<f64>() / pool.current_servers.len().max(1) as f64,
                monthly_cost: pool.current_servers.len() as f64 * pool.cost_per_server,
                auto_scaling_enabled: pool.auto_scaling_config.enabled,
            };
            status.insert(pool_type.clone(), pool_status);
        }

        Ok(status)
    }

    #[instrument(skip(self))]
    pub async fn calculate_profit_analysis(&self, user_distribution: &HashMap<String, u32>) -> ServiceResult<ProfitAnalysis> {
        let mut total_revenue = 0.0;
        let mut total_cost = 0.0;
        let mut plan_breakdown = Vec::new();

        for (plan_name, user_count) in user_distribution {
            let plan = self.get_hosting_plan(plan_name)?;
            let plan_revenue = plan.monthly_price_usd * (*user_count as f64);
            total_revenue += plan_revenue;

            plan_breakdown.push(PlanRevenue {
                plan_name: plan_name.clone(),
                user_count: *user_count,
                monthly_price: plan.monthly_price_usd,
                total_revenue: plan_revenue,
            });
        }

        // Calculate infrastructure costs
        let pools_status = self.get_server_pools_status().await?;
        for (_, pool_status) in pools_status {
            total_cost += pool_status.monthly_cost;
        }

        let profit = total_revenue - total_cost;
        let profit_margin = if total_revenue > 0.0 { (profit / total_revenue) * 100.0 } else { 0.0 };

        Ok(ProfitAnalysis {
            total_revenue,
            total_cost,
            profit,
            profit_margin,
            plan_breakdown,
            break_even_users: (total_cost / 1.44).ceil() as u32, // Based on nano plan price
        })
    }

    #[instrument(skip(self))]
    pub async fn auto_scale_check(&self) -> ServiceResult<Vec<ScalingAction>> {
        let mut actions = Vec::new();

        for (pool_type, pool) in &self.server_pools {
            if !pool.auto_scaling_config.enabled {
                continue;
            }

            for server in &pool.current_servers {
                // Check scale up conditions
                if server.cpu_utilization > 80.0 || server.memory_utilization > 85.0 || server.current_users > 90 {
                    actions.push(ScalingAction {
                        action_type: ScalingActionType::ScaleUp,
                        pool_type: pool_type.clone(),
                        server_id: Some(server.instance_id.clone()),
                        reason: format!("High utilization: CPU {}%, Memory {}%, Users {}",
                                      server.cpu_utilization, server.memory_utilization, server.current_users),
                        estimated_cost: pool.cost_per_server,
                    });
                }

                // Check scale down conditions
                if server.cpu_utilization < 30.0 && server.memory_utilization < 40.0 && server.current_users < 50 {
                    actions.push(ScalingAction {
                        action_type: ScalingActionType::ScaleDown,
                        pool_type: pool_type.clone(),
                        server_id: Some(server.instance_id.clone()),
                        reason: format!("Low utilization: CPU {}%, Memory {}%, Users {}",
                                      server.cpu_utilization, server.memory_utilization, server.current_users),
                        estimated_cost: -pool.cost_per_server,
                    });
                }
            }

            // Check if we need new servers
            if pool.current_servers.is_empty() || pool.current_servers.iter().all(|s| s.current_users >= pool.max_users_per_server * 90 / 100) {
                actions.push(ScalingAction {
                    action_type: ScalingActionType::AddServer,
                    pool_type: pool_type.clone(),
                    server_id: None,
                    reason: "Pool at capacity, need new server".to_string(),
                    estimated_cost: pool.cost_per_server,
                });
            }
        }

        Ok(actions)
    }

    // Helper methods
    pub fn get_hosting_plan(&self, plan_name: &str) -> ServiceResult<HostingPlan> {
        let plan = match plan_name {
            "starter" => HostingPlan {
                name: "starter".to_string(),
                monthly_price_usd: 0.99,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 1485.0),
                    ("kenya_kes".to_string(), 129.0),
                    ("south_africa_zar".to_string(), 18.0),
                    ("ghana_ghs".to_string(), 12.0),
                    ("egypt_egp".to_string(), 31.0),
                    ("morocco_mad".to_string(), 10.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(256),
                    memory_reservation: Some("8Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "500MB".to_string(),
                    bandwidth_limit: "5GB/month".to_string(),
                    domains: Some(1),
                    databases: Some(1),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "20-100x during low traffic".to_string(),
                }),
                priority_weight: 1,
                target_users: "Students, personal projects, learning".to_string(),
            },
            "nano" => HostingPlan {
                name: "nano".to_string(),
                monthly_price_usd: 1.44,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 2160.0),
                    ("kenya_kes".to_string(), 187.0),
                    ("south_africa_zar".to_string(), 26.0),
                    ("ghana_ghs".to_string(), 17.0),
                    ("egypt_egp".to_string(), 45.0),
                    ("morocco_mad".to_string(), 15.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(512),
                    memory_reservation: Some("10Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "1GB".to_string(),
                    bandwidth_limit: "10GB/month".to_string(),
                    domains: Some(3),
                    databases: Some(2),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "10-50x during low traffic".to_string(),
                }),
                priority_weight: 2,
                target_users: "Small websites, APIs, microservices".to_string(),
            },
            "micro" => HostingPlan {
                name: "micro".to_string(),
                monthly_price_usd: 2.88,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 4320.0),
                    ("kenya_kes".to_string(), 374.0),
                    ("south_africa_zar".to_string(), 52.0),
                    ("ghana_ghs".to_string(), 35.0),
                    ("egypt_egp".to_string(), 90.0),
                    ("morocco_mad".to_string(), 30.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(1024),
                    memory_reservation: Some("20Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "2GB".to_string(),
                    bandwidth_limit: "25GB/month".to_string(),
                    domains: Some(5),
                    databases: Some(3),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "5-25x during low traffic".to_string(),
                }),
                priority_weight: 4,
                target_users: "Small businesses, growing startups".to_string(),
            },
            "small" => HostingPlan {
                name: "small".to_string(),
                monthly_price_usd: 5.76,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 8640.0),
                    ("kenya_kes".to_string(), 748.0),
                    ("south_africa_zar".to_string(), 104.0),
                    ("ghana_ghs".to_string(), 69.0),
                    ("egypt_egp".to_string(), 180.0),
                    ("morocco_mad".to_string(), 60.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(2048),
                    memory_reservation: Some("40Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "5GB".to_string(),
                    bandwidth_limit: "50GB/month".to_string(),
                    domains: Some(10),
                    databases: Some(5),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "3-15x during low traffic".to_string(),
                }),
                priority_weight: 8,
                target_users: "Medium businesses, production apps".to_string(),
            },
            "business" => HostingPlan {
                name: "business".to_string(),
                monthly_price_usd: 11.52,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 17280.0),
                    ("kenya_kes".to_string(), 1497.0),
                    ("south_africa_zar".to_string(), 208.0),
                    ("ghana_ghs".to_string(), 138.0),
                    ("egypt_egp".to_string(), 360.0),
                    ("morocco_mad".to_string(), 120.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: Some(4096),
                    memory_reservation: Some("80Mi".to_string()),
                    memory_limit: None,
                    storage_limit: "10GB".to_string(),
                    bandwidth_limit: "100GB/month".to_string(),
                    domains: Some(25),
                    databases: Some(10),
                    vcpu: None,
                    memory_gb: None,
                    guaranteed_resources: Some(false),
                },
                burst_capability: Some(BurstCapability {
                    cpu_burst: "Up to 1000m when server idle".to_string(),
                    memory_burst: "Up to 800Mi when available".to_string(),
                    performance_multiplier: "2-10x during low traffic".to_string(),
                }),
                priority_weight: 16,
                target_users: "Established businesses, high-traffic sites".to_string(),
            },
            "dedicated_small" => HostingPlan {
                name: "dedicated_small".to_string(),
                monthly_price_usd: 25.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 37500.0),
                    ("kenya_kes".to_string(), 3250.0),
                    ("south_africa_zar".to_string(), 450.0),
                    ("ghana_ghs".to_string(), 300.0),
                    ("egypt_egp".to_string(), 780.0),
                    ("morocco_mad".to_string(), 260.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "80GB".to_string(),
                    bandwidth_limit: "2TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(2),
                    memory_gb: Some(4),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 32,
                target_users: "Agencies, SaaS startups, e-commerce".to_string(),
            },
            "dedicated_medium" => HostingPlan {
                name: "dedicated_medium".to_string(),
                monthly_price_usd: 50.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 75000.0),
                    ("kenya_kes".to_string(), 6500.0),
                    ("south_africa_zar".to_string(), 900.0),
                    ("ghana_ghs".to_string(), 600.0),
                    ("egypt_egp".to_string(), 1560.0),
                    ("morocco_mad".to_string(), 520.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "160GB".to_string(),
                    bandwidth_limit: "4TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(4),
                    memory_gb: Some(8),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 64,
                target_users: "Growing SaaS, fintech, enterprise apps".to_string(),
            },
            "dedicated_large" => HostingPlan {
                name: "dedicated_large".to_string(),
                monthly_price_usd: 100.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 150000.0),
                    ("kenya_kes".to_string(), 13000.0),
                    ("south_africa_zar".to_string(), 1800.0),
                    ("ghana_ghs".to_string(), 1200.0),
                    ("egypt_egp".to_string(), 3120.0),
                    ("morocco_mad".to_string(), 1040.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "320GB".to_string(),
                    bandwidth_limit: "8TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(8),
                    memory_gb: Some(16),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 128,
                target_users: "Large enterprises, high-performance apps".to_string(),
            },
            "enterprise_standard" => HostingPlan {
                name: "enterprise_standard".to_string(),
                monthly_price_usd: 200.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 300000.0),
                    ("kenya_kes".to_string(), 26000.0),
                    ("south_africa_zar".to_string(), 3600.0),
                    ("ghana_ghs".to_string(), 2400.0),
                    ("egypt_egp".to_string(), 6240.0),
                    ("morocco_mad".to_string(), 2080.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "640GB".to_string(),
                    bandwidth_limit: "16TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(16),
                    memory_gb: Some(32),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 256,
                target_users: "Large enterprises, mission-critical apps".to_string(),
            },
            "enterprise_premium" => HostingPlan {
                name: "enterprise_premium".to_string(),
                monthly_price_usd: 400.00,
                local_pricing: [
                    ("nigeria_ngn".to_string(), 600000.0),
                    ("kenya_kes".to_string(), 52000.0),
                    ("south_africa_zar".to_string(), 7200.0),
                    ("ghana_ghs".to_string(), 4800.0),
                    ("egypt_egp".to_string(), 12480.0),
                    ("morocco_mad".to_string(), 4160.0),
                ].iter().cloned().collect(),
                resource_allocation: ResourceAllocation {
                    cpu_shares: None,
                    memory_reservation: None,
                    memory_limit: None,
                    storage_limit: "1280GB".to_string(),
                    bandwidth_limit: "32TB/month".to_string(),
                    domains: None,
                    databases: None,
                    vcpu: Some(32),
                    memory_gb: Some(64),
                    guaranteed_resources: Some(true),
                },
                burst_capability: None,
                priority_weight: 512,
                target_users: "Fortune 500, banks, government".to_string(),
            },
            _ => return Err(ServiceError::NotFound(format!("Hosting plan '{}' not found", plan_name))),
        };

        Ok(plan)
    }

    fn determine_hosting_tier(&self, plan: &str) -> HostingTierType {
        match plan {
            "starter" | "nano" | "micro" | "small" | "business" => HostingTierType::Shared,
            "dedicated_small" | "dedicated_medium" | "dedicated_large" => HostingTierType::Dedicated,
            "enterprise_standard" | "enterprise_premium" => HostingTierType::Enterprise,
            _ => HostingTierType::Shared,
        }
    }

    async fn find_best_server_for_user(&self, pool_type: &PoolType, region: &str) -> ServiceResult<Server> {
        // Query actual servers from Vultr API
        let instances = self.vultr_client.list_instances().await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to list instances: {}", e)))?;

        // Filter servers by pool type and region
        let suitable_servers: Vec<Server> = instances
            .into_iter()
            .filter(|instance| {
                instance.region == region &&
                instance.status == "active" &&
                self.matches_pool_type(&instance.plan, pool_type)
            })
            .map(|instance| self.convert_instance_to_server(instance, pool_type.clone()))
            .collect();

        // Find server with lowest utilization
        suitable_servers
            .into_iter()
            .min_by(|a, b| {
                let a_utilization = (a.current_users as f64 / a.max_users as f64) * 100.0;
                let b_utilization = (b.current_users as f64 / b.max_users as f64) * 100.0;
                a_utilization.partial_cmp(&b_utilization).unwrap_or(std::cmp::Ordering::Equal)
            })
            .ok_or_else(|| ServiceError::Internal(format!("No suitable servers found in region: {}", region)))
    }

    fn generate_container_config(&self, plan: &HostingPlan) -> String {
        format!(
            "--cpu-shares={} --memory-reservation={}",
            plan.resource_allocation.cpu_shares.unwrap_or(512),
            plan.resource_allocation.memory_reservation.as_ref().unwrap_or(&"10m".to_string())
        )
    }

    async fn deploy_container(&self, user_id: &str, config: &str, server_id: &str) -> ServiceResult<String> {
        // Deploy container using SSH to the target server
        let container_name = format!("achidas-{}", user_id);

        // Execute Docker deployment command via SSH
        let deploy_command = format!(
            "docker run -d --name {} {} --restart unless-stopped",
            container_name, config
        );

        // In production, this would use SSH client to execute on the target server
        let output = tokio::process::Command::new("ssh")
            .arg(format!("root@{}", server_id))
            .arg(&deploy_command)
            .output()
            .await
            .map_err(|e| ServiceError::Internal(format!("Failed to deploy container: {}", e)))?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Internal(format!("Container deployment failed: {}", error)));
        }

        info!("Successfully deployed container {} on server {}", container_name, server_id);
        Ok(container_name)
    }

    async fn create_dedicated_vm(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {
        // Create dedicated VM using Vultr API
        let plan_id = self.select_vultr_plan_for_specs(specs)?;

        let create_request = crate::vultr::models::CreateVultrInstanceRequest {
            region: region.to_string(),
            plan: plan_id,
            os_id: Some(387), // Ubuntu 20.04 LTS
            image_id: None,
            label: Some(format!("achidas-dedicated-{}", user_id)),
            hostname: None,
            tag: Some("achidas-dedicated".to_string()),
            user_data: Some(self.generate_user_data_script(user_id)),
            ssh_keys: None,
            startup_script_id: None,
            firewall_group_id: None,
            enable_ipv6: Some(true),
            enable_private_network: Some(false),
            attach_private_network: None,
            enable_ddos_protection: Some(true),
            backups: None,
            app_id: None,
            snapshot_id: None,
            iso_id: None,
            script_id: None,
            activation_email: None,
            ddos_protection: None,
            enable_vpc: Some(false),
            attach_vpc: None,
            tags: Some(vec!["achidas".to_string(), "dedicated".to_string()]),
        };

        let instance = self.vultr_client.create_instance(create_request).await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to create dedicated VM: {}", e)))?;

        info!("Successfully created dedicated VM {} for user {}", instance.id, user_id);
        Ok(instance.id)
    }

    async fn create_enterprise_server(&self, user_id: &str, specs: &ResourceAllocation, region: &str) -> ServiceResult<String> {
        // Create enterprise server with high-performance specs
        let plan_id = self.select_enterprise_plan_for_specs(specs)?;

        let create_request = crate::vultr::models::CreateVultrInstanceRequest {
            region: region.to_string(),
            plan: plan_id,
            os_id: Some(387), // Ubuntu 20.04 LTS
            image_id: None,
            label: Some(format!("achidas-enterprise-{}", user_id)),
            hostname: None,
            tag: Some("achidas-enterprise".to_string()),
            user_data: Some(self.generate_enterprise_user_data_script(user_id)),
            ssh_keys: None,
            startup_script_id: None,
            firewall_group_id: None,
            enable_ipv6: Some(true),
            enable_private_network: Some(true),
            attach_private_network: None,
            enable_ddos_protection: Some(true),
            backups: None,
            app_id: None,
            snapshot_id: None,
            iso_id: None,
            script_id: None,
            activation_email: None,
            ddos_protection: None,
            enable_vpc: Some(true),
            attach_vpc: None,
            tags: Some(vec!["achidas".to_string(), "enterprise".to_string()]),
        };

        let instance = self.vultr_client.create_instance(create_request).await
            .map_err(|e| ServiceError::ExternalApi(format!("Failed to create enterprise server: {}", e)))?;

        // Set up enterprise-specific configurations
        self.configure_enterprise_server(&instance.id, user_id).await?;

        info!("Successfully created enterprise server {} for user {}", instance.id, user_id);
        Ok(instance.id)
    }

    // Production helper methods
    fn matches_pool_type(&self, plan: &str, pool_type: &PoolType) -> bool {
        match pool_type {
            PoolType::SharedHot => plan.contains("vhf") || plan.contains("vhp"), // High-frequency/performance plans
            PoolType::SharedCold => plan.contains("vc2") || plan.contains("voc"), // Regular compute plans
            PoolType::Dedicated => plan.contains("vc2") && (plan.contains("2c") || plan.contains("4c") || plan.contains("8c")),
            PoolType::Enterprise => plan.contains("vc2") && (plan.contains("16c") || plan.contains("32c")),
        }
    }

    fn convert_instance_to_server(&self, instance: crate::vultr::models::VultrInstanceDetailed, pool_type: PoolType) -> Server {
        // Convert Vultr instance to our Server model
        // This would include querying current utilization metrics
        let plan = instance.plan.clone();
        Server {
            instance_id: instance.id,
            server_type: instance.plan,
            pool_type,
            current_users: 0, // Would be queried from monitoring system
            max_users: self.calculate_max_users_for_plan(&plan),
            cpu_utilization: 0.0, // Would be queried from monitoring
            memory_utilization: 0.0, // Would be queried from monitoring
            status: if instance.status == "active" { ServerStatus::Active } else { ServerStatus::Inactive },
            region: instance.region,
            monthly_cost: self.calculate_monthly_cost_for_plan(&plan),
        }
    }

    fn calculate_max_users_for_plan(&self, plan: &str) -> u32 {
        // Calculate max users based on plan specifications
        match plan {
            p if p.contains("1c-1gb") => 50,
            p if p.contains("1c-2gb") => 75,
            p if p.contains("2c-2gb") => 100,
            p if p.contains("2c-4gb") => 150,
            p if p.contains("4c-8gb") => 300,
            _ => 25, // Conservative default
        }
    }

    fn calculate_monthly_cost_for_plan(&self, plan: &str) -> f64 {
        // Calculate monthly cost based on plan specifications
        match plan {
            "vhf-1c-1gb" => 6.00,
            "vc2-1c-1gb" => 5.00,
            "vc2-1c-2gb" => 10.00,
            "vc2-2c-2gb" => 12.00,
            "vc2-2c-4gb" => 24.00,
            "vc2-4c-8gb" => 48.00,
            "vc2-8c-16gb" => 96.00,
            "vc2-16c-32gb" => 192.00,
            "vc2-32c-64gb" => 384.00,
            _ => 10.00, // Default fallback
        }
    }

    fn select_vultr_plan_for_specs(&self, specs: &ResourceAllocation) -> ServiceResult<String> {
        // Map resource specs to Vultr plan IDs
        let cpu_cores = specs.cpu_shares.unwrap_or(1);
        let memory_gb = specs.memory_gb.unwrap_or(1);

        match (cpu_cores, memory_gb as u32) {
            (1, 1) => Ok("vc2-1c-1gb".to_string()),
            (1, 2) => Ok("vc2-1c-2gb".to_string()),
            (2, 2) => Ok("vc2-2c-2gb".to_string()),
            (2, 4) => Ok("vc2-2c-4gb".to_string()),
            (4, 8) => Ok("vc2-4c-8gb".to_string()),
            _ => Ok("vc2-1c-1gb".to_string()), // Default fallback
        }
    }

    fn select_enterprise_plan_for_specs(&self, specs: &ResourceAllocation) -> ServiceResult<String> {
        // Map to high-performance plans for enterprise
        let cpu_cores = specs.cpu_shares.unwrap_or(2);
        let memory_gb = specs.memory_gb.unwrap_or(4);

        match (cpu_cores, memory_gb as u32) {
            (1..=2, 1..=4) => Ok("vhf-2c-4gb".to_string()),
            (3..=4, 5..=8) => Ok("vhf-4c-8gb".to_string()),
            (5..=8, 9..=16) => Ok("vhf-8c-16gb".to_string()),
            _ => Ok("vhf-2c-4gb".to_string()), // Default enterprise plan
        }
    }

    fn generate_user_data_script(&self, user_id: &str) -> String {
        format!(r#"#!/bin/bash
# Achidas dedicated server setup for user: {}
apt-get update
apt-get install -y docker.io nginx
systemctl enable docker
systemctl start docker
systemctl enable nginx
systemctl start nginx

# Create user directory
mkdir -p /opt/achidas/users/{}
chown -R www-data:www-data /opt/achidas/users/{}

# Configure firewall
ufw allow ssh
ufw allow http
ufw allow https
ufw --force enable
"#, user_id, user_id, user_id)
    }

    fn generate_enterprise_user_data_script(&self, user_id: &str) -> String {
        format!(r#"#!/bin/bash
# Achidas enterprise server setup for user: {}
apt-get update
apt-get install -y docker.io nginx redis-server postgresql-12
systemctl enable docker redis-server postgresql
systemctl start docker redis-server postgresql

# Create user directory with enterprise features
mkdir -p /opt/achidas/enterprise/{}
mkdir -p /opt/achidas/enterprise/{}/backups
mkdir -p /opt/achidas/enterprise/{}/logs
chown -R www-data:www-data /opt/achidas/enterprise/{}

# Configure enterprise firewall
ufw allow ssh
ufw allow http
ufw allow https
ufw allow 5432/tcp  # PostgreSQL
ufw allow 6379/tcp  # Redis
ufw --force enable

# Set up monitoring
apt-get install -y prometheus-node-exporter
systemctl enable prometheus-node-exporter
systemctl start prometheus-node-exporter
"#, user_id, user_id, user_id, user_id, user_id)
    }

    async fn configure_enterprise_server(&self, instance_id: &str, user_id: &str) -> ServiceResult<()> {
        // Additional enterprise configurations
        info!("Configuring enterprise features for server {} and user {}", instance_id, user_id);

        // This would include:
        // - Setting up load balancers
        // - Configuring backup systems
        // - Setting up monitoring and alerting
        // - Configuring high availability

        Ok(())
    }
}

// Helper functions
fn pool_type_to_string(pool_type: &PoolType) -> &str {
    match pool_type {
        PoolType::SharedHot => "shared-hot",
        PoolType::SharedCold => "shared-cold",
        PoolType::Dedicated => "dedicated",
        PoolType::Enterprise => "enterprise",
    }
}

fn is_suitable_for_achidas(plan: &str) -> bool {
    matches!(plan,
        "vhf-1c-1gb" | "vc2-1c-1gb" | "vc2-2c-4gb" | "vc2-4c-8gb" |
        "vc2-8c-16gb" | "vc2-16c-32gb" | "vc2-32c-64gb"
    )
}
